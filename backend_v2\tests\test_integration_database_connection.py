"""
数据库连接和会话管理集成测试
测试数据库连接、会话创建、连接池等功能
"""
import pytest
import time
import threading
from unittest.mock import patch, Mock
from sqlalchemy import text, create_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.exc import OperationalError, DisconnectionError
from sqlalchemy.pool import StaticPool

from app.core.database import Base
from app.core.config import settings


# 创建测试专用的数据库引擎和会话
@pytest.fixture(scope="module")
def test_db_engine():
    """创建测试数据库引擎"""
    # 使用内存SQLite数据库进行测试
    engine = create_engine(
        "sqlite:///:memory:",
        echo=False,
        pool_pre_ping=True,
        pool_recycle=300,
        pool_size=10,
        max_overflow=20,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )

    # 创建所有表
    Base.metadata.create_all(bind=engine)

    yield engine

    # 清理
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_session_factory(test_db_engine):
    """创建测试会话工厂"""
    return sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_db_engine,
        expire_on_commit=False
    )


@pytest.mark.integration
class TestDatabaseConnection:
    """测试数据库连接功能"""

    def test_engine_configuration(self, test_db_engine):
        """测试数据库引擎配置"""
        # 验证引擎配置
        assert test_db_engine is not None
        assert test_db_engine.pool_size == 10
        assert test_db_engine.pool._max_overflow == 20
        assert test_db_engine.pool._recycle == 300

        # 验证连接参数
        assert test_db_engine.pool._pre_ping is True
    
    def test_database_connection_basic(self, test_db_engine):
        """测试基本数据库连接"""
        # 测试连接是否可用
        with test_db_engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            assert row[0] == 1
    
    def test_session_creation_and_cleanup(self, test_session_factory, test_db_engine):
        """测试会话创建和清理"""
        # 测试会话工厂创建会话
        session = test_session_factory()

        try:
            # 验证会话可用
            assert isinstance(session, Session)
            assert session.bind is test_db_engine

            # 测试简单查询
            result = session.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            assert row[0] == 1

        finally:
            session.close()
    
    def test_get_db_dependency_injection(self):
        """测试get_db依赖注入函数"""
        # 测试get_db生成器
        db_generator = get_db()
        
        try:
            db = next(db_generator)
            
            # 验证返回的是Session对象
            assert isinstance(db, Session)
            
            # 测试会话可用性
            result = db.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            assert row[0] == 1
            
        except StopIteration:
            pytest.fail("get_db generator should yield a session")
        finally:
            # 清理生成器
            try:
                next(db_generator)
            except StopIteration:
                pass  # 正常结束
    
    def test_get_db_session_manual(self):
        """测试手动获取数据库会话"""
        session = get_db_session()
        
        try:
            # 验证会话类型
            assert isinstance(session, Session)
            
            # 测试会话功能
            result = session.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            assert row[0] == 1
            
        finally:
            session.close()
    
    def test_session_isolation(self):
        """测试会话隔离性"""
        session1 = get_db_session()
        session2 = get_db_session()
        
        try:
            # 验证是不同的会话对象
            assert session1 is not session2
            assert id(session1) != id(session2)
            
            # 两个会话都应该可用
            result1 = session1.execute(text("SELECT 1 as test_value"))
            result2 = session2.execute(text("SELECT 2 as test_value"))
            
            assert result1.fetchone()[0] == 1
            assert result2.fetchone()[0] == 2
            
        finally:
            session1.close()
            session2.close()


@pytest.mark.integration
class TestDatabaseConnectionPool:
    """测试数据库连接池功能"""
    
    def test_connection_pool_basic(self):
        """测试连接池基本功能"""
        connections = []
        
        try:
            # 创建多个连接
            for i in range(5):
                conn = engine.connect()
                connections.append(conn)
                
                # 验证连接可用
                result = conn.execute(text(f"SELECT {i+1} as test_value"))
                assert result.fetchone()[0] == i + 1
            
            # 验证连接池状态
            pool = engine.pool
            assert pool.checkedout() == 5  # 5个连接被检出
            
        finally:
            # 清理连接
            for conn in connections:
                conn.close()
    
    def test_connection_pool_overflow(self):
        """测试连接池溢出处理"""
        connections = []
        
        try:
            # 尝试创建超过pool_size + max_overflow的连接
            # pool_size=10, max_overflow=20，所以最多30个连接
            for i in range(15):  # 创建15个连接，应该都成功
                conn = engine.connect()
                connections.append(conn)
                
                # 验证连接可用
                result = conn.execute(text(f"SELECT {i+1} as test_value"))
                assert result.fetchone()[0] == i + 1
            
            # 验证连接池状态
            pool = engine.pool
            assert pool.checkedout() == 15
            
        finally:
            # 清理连接
            for conn in connections:
                conn.close()
    
    def test_connection_pool_concurrent_access(self):
        """测试连接池并发访问"""
        results = []
        errors = []
        
        def worker(worker_id):
            """工作线程函数"""
            try:
                session = get_db_session()
                try:
                    # 模拟一些数据库操作
                    result = session.execute(text(f"SELECT {worker_id} as worker_id"))
                    value = result.fetchone()[0]
                    results.append(value)
                    
                    # 模拟一些处理时间
                    time.sleep(0.01)
                    
                finally:
                    session.close()
            except Exception as e:
                errors.append(str(e))
        
        # 创建多个线程并发访问数据库
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0, f"Concurrent access errors: {errors}"
        assert len(results) == 10
        assert sorted(results) == list(range(10))
    
    def test_connection_recovery_after_disconnect(self):
        """测试连接断开后的恢复"""
        # 获取一个连接
        session = get_db_session()
        
        try:
            # 正常查询
            result = session.execute(text("SELECT 1 as test_value"))
            assert result.fetchone()[0] == 1
            
            # 模拟连接断开（通过关闭底层连接）
            # 注意：这个测试可能需要根据实际数据库类型调整
            session.close()
            
            # 创建新会话，应该能够正常工作
            new_session = get_db_session()
            try:
                result = new_session.execute(text("SELECT 2 as test_value"))
                assert result.fetchone()[0] == 2
            finally:
                new_session.close()
                
        finally:
            if session:
                session.close()


@pytest.mark.integration
class TestDatabaseErrorHandling:
    """测试数据库错误处理"""
    
    def test_get_db_error_handling(self):
        """测试get_db的错误处理"""
        # Mock一个会抛出异常的SessionLocal
        with patch('app.core.database.SessionLocal') as mock_session_local:
            mock_session = Mock()
            mock_session.execute.side_effect = Exception("Database error")
            mock_session_local.return_value = mock_session
            
            db_generator = get_db()
            
            try:
                db = next(db_generator)
                
                # 执行会导致异常的操作
                with pytest.raises(Exception, match="Database error"):
                    db.execute(text("SELECT 1"))
                
                # 验证rollback被调用
                mock_session.rollback.assert_called_once()
                
            except StopIteration:
                pytest.fail("get_db generator should yield a session")
            finally:
                # 清理生成器
                try:
                    next(db_generator)
                except StopIteration:
                    pass
                
                # 验证close被调用
                mock_session.close.assert_called_once()
    
    def test_session_timeout_handling(self):
        """测试会话超时处理"""
        session = get_db_session()
        
        try:
            # 测试正常查询
            result = session.execute(text("SELECT 1 as test_value"))
            assert result.fetchone()[0] == 1
            
            # 对于PostgreSQL，可以测试语句超时
            if 'postgresql' in str(engine.url):
                # 设置一个很短的超时时间
                session.execute(text("SET statement_timeout = '1ms'"))
                
                # 执行一个可能超时的查询
                with pytest.raises(OperationalError):
                    session.execute(text("SELECT pg_sleep(1)"))  # 睡眠1秒，应该超时
                    
        except Exception as e:
            # 如果不是PostgreSQL或者其他原因，跳过这个测试
            pytest.skip(f"Timeout test not applicable: {e}")
        finally:
            session.close()
    
    @patch('app.core.database.logger')
    def test_connection_logging(self, mock_logger):
        """测试连接日志记录"""
        # 创建和关闭会话，应该触发日志记录
        session = get_db_session()
        session_id = id(session)
        
        try:
            # 执行一个查询
            session.execute(text("SELECT 1"))
        finally:
            session.close()
        
        # 验证日志调用（这个测试可能需要根据实际日志实现调整）
        # 注意：由于日志是在事件监听器中触发的，可能需要特殊处理
        assert mock_logger.debug.called
