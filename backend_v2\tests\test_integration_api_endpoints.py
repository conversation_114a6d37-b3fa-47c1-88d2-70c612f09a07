"""
API端点集成测试
测试FastAPI应用的所有端点，包括基础端点、任务API、文件API等
"""
import pytest
import json
import time
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db, SessionLocal
from app.services.minio_service import get_minio_service
from app.core.config import settings


@pytest.fixture(scope="module")
def test_client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture(scope="function")
def override_dependencies():
    """覆盖依赖注入"""
    # 保存原始依赖
    original_overrides = app.dependency_overrides.copy()
    
    # Mock数据库依赖
    def mock_get_db():
        db = Mock(spec=Session)
        return db
    
    # Mock MinIO服务依赖
    def mock_get_minio_service():
        service = Mock()
        service.initialize.return_value = True
        service.check_connection.return_value = True
        service.generate_presigned_put_url.return_value = "https://test-presigned-url"
        service.upload_file.return_value = True
        service.download_file.return_value = b"test file content"
        service.delete_file.return_value = True
        service.file_exists.return_value = True
        service.get_file_info.return_value = {
            "size": 1024,
            "content_type": "text/plain",
            "last_modified": "2025-01-05T10:00:00Z"
        }
        return service
    
    # 设置依赖覆盖
    app.dependency_overrides[get_db] = mock_get_db
    app.dependency_overrides[get_minio_service] = mock_get_minio_service
    
    yield
    
    # 恢复原始依赖
    app.dependency_overrides = original_overrides


@pytest.mark.integration
class TestBasicEndpoints:
    """测试基础API端点"""
    
    def test_root_endpoint(self, test_client):
        """测试根路径端点"""
        response = test_client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert "message" in data
        assert "version" in data
        assert "docs" in data
        assert "health" in data
        
        # 验证内容
        assert settings.APP_NAME in data["message"]
        assert data["version"] == settings.APP_VERSION
        assert data["docs"] == "/docs"
        assert data["health"] == "/health"
    
    def test_health_check_endpoint(self, test_client):
        """测试健康检查端点"""
        response = test_client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert "status" in data
        assert "app_name" in data
        assert "version" in data
        assert "environment" in data
        
        # 验证内容
        assert data["status"] == "healthy"
        assert data["app_name"] == settings.APP_NAME
        assert data["version"] == settings.APP_VERSION
        assert data["environment"] == settings.ENVIRONMENT
    
    def test_docs_endpoint_in_debug_mode(self, test_client):
        """测试文档端点（调试模式）"""
        with patch.object(settings, 'DEBUG', True):
            response = test_client.get("/docs")
            # 在调试模式下，文档应该可访问
            # 注意：这可能返回HTML，所以我们只检查状态码
            assert response.status_code in [200, 404]  # 404如果没有正确配置
    
    def test_openapi_endpoint_in_debug_mode(self, test_client):
        """测试OpenAPI端点（调试模式）"""
        with patch.object(settings, 'DEBUG', True):
            response = test_client.get("/openapi.json")
            if response.status_code == 200:
                data = response.json()
                assert "openapi" in data
                assert "info" in data
                assert "paths" in data


@pytest.mark.integration
class TestMiddlewareFunctionality:
    """测试中间件功能"""
    
    def test_cors_headers(self, test_client):
        """测试CORS头部"""
        # 发送带Origin的请求
        headers = {"Origin": "http://localhost:3000"}
        response = test_client.get("/health", headers=headers)
        
        assert response.status_code == 200
        # 检查CORS相关头部（如果配置了的话）
        # 注意：具体的CORS头部取决于配置
    
    def test_process_time_header(self, test_client):
        """测试请求处理时间头部"""
        response = test_client.get("/health")
        
        assert response.status_code == 200
        # 检查是否有处理时间头部
        assert "X-Process-Time" in response.headers
        
        # 验证处理时间是有效的数字
        process_time = float(response.headers["X-Process-Time"])
        assert process_time >= 0
        assert process_time < 10  # 应该很快
    
    def test_request_logging(self, test_client):
        """测试请求日志记录"""
        # 这个测试主要验证请求不会因为日志记录而失败
        response = test_client.get("/health")
        assert response.status_code == 200
        
        # 发送POST请求
        response = test_client.post("/api/files/presign", 
                                  params={"filename": "test.jpg", "file_type": "image/jpeg"})
        # 不管结果如何，至少不应该因为日志记录而崩溃
        assert response.status_code in [200, 400, 422, 500]


@pytest.mark.integration
class TestTasksAPI:
    """测试任务API端点"""
    
    def test_tasks_endpoint_structure(self, test_client, override_dependencies):
        """测试任务端点结构"""
        # 由于任务API可能需要复杂的数据库操作，我们先测试端点是否存在
        # 这里我们测试一些基本的端点访问
        
        # 测试获取任务列表（可能返回空列表或错误）
        response = test_client.get("/api/tasks/")
        # 端点应该存在，不管返回什么
        assert response.status_code in [200, 404, 422, 500]
    
    def test_task_creation_endpoint_exists(self, test_client, override_dependencies):
        """测试任务创建端点存在性"""
        # 测试POST端点是否存在
        response = test_client.post("/api/tasks/", json={})
        # 端点应该存在，可能返回验证错误
        assert response.status_code in [200, 400, 422, 500]
        # 不应该返回404（端点不存在）
        assert response.status_code != 404


@pytest.mark.integration
class TestFilesAPI:
    """测试文件API端点"""
    
    def test_presigned_url_endpoint_valid_request(self, test_client, override_dependencies):
        """测试预签名URL端点 - 有效请求"""
        with patch('app.api.files.settings') as mock_settings:
            # 配置允许的文件类型
            mock_settings.get_allowed_image_extensions.return_value = ['jpg', 'png', 'gif']
            mock_settings.get_allowed_video_extensions.return_value = ['mp4', 'avi']
            
            response = test_client.post("/api/files/presign", 
                                      params={
                                          "filename": "test.jpg",
                                          "file_type": "image/jpeg",
                                          "expires_hours": 2
                                      })
            
            # 应该成功或返回可预期的错误
            assert response.status_code in [200, 400, 422, 500]
            
            if response.status_code == 200:
                data = response.json()
                assert "upload_url" in data or "presigned_url" in data
    
    def test_presigned_url_endpoint_invalid_file_type(self, test_client, override_dependencies):
        """测试预签名URL端点 - 无效文件类型"""
        with patch('app.api.files.settings') as mock_settings:
            # 配置允许的文件类型
            mock_settings.get_allowed_image_extensions.return_value = ['jpg', 'png']
            mock_settings.get_allowed_video_extensions.return_value = ['mp4']
            
            response = test_client.post("/api/files/presign", 
                                      params={
                                          "filename": "test.exe",  # 不允许的类型
                                          "file_type": "application/exe"
                                      })
            
            # 应该返回400错误
            assert response.status_code == 400
            data = response.json()
            assert "detail" in data
            assert "not allowed" in data["detail"].lower()
    
    def test_presigned_url_endpoint_invalid_expires_hours(self, test_client, override_dependencies):
        """测试预签名URL端点 - 无效过期时间"""
        with patch('app.api.files.settings') as mock_settings:
            mock_settings.get_allowed_image_extensions.return_value = ['jpg']
            mock_settings.get_allowed_video_extensions.return_value = ['mp4']
            
            # 测试过期时间太长
            response = test_client.post("/api/files/presign", 
                                      params={
                                          "filename": "test.jpg",
                                          "file_type": "image/jpeg",
                                          "expires_hours": 25  # 超过24小时
                                      })
            
            assert response.status_code == 400
            data = response.json()
            assert "detail" in data
            assert "between 1 and 24" in data["detail"]
    
    def test_files_endpoint_missing_parameters(self, test_client, override_dependencies):
        """测试文件端点 - 缺少参数"""
        # 不提供必需的参数
        response = test_client.post("/api/files/presign")
        
        # 应该返回422验证错误
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data


@pytest.mark.integration
class TestErrorHandling:
    """测试错误处理"""
    
    def test_404_error_handling(self, test_client):
        """测试404错误处理"""
        response = test_client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
    
    def test_method_not_allowed_error(self, test_client):
        """测试方法不允许错误"""
        # 对只支持GET的端点发送POST请求
        response = test_client.post("/health")
        
        assert response.status_code == 405
        data = response.json()
        assert "detail" in data
    
    def test_validation_error_handling(self, test_client, override_dependencies):
        """测试验证错误处理"""
        # 发送无效的JSON数据
        response = test_client.post("/api/files/presign", 
                                  json={"invalid": "data"})
        
        # 应该返回422验证错误
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
        assert isinstance(data["detail"], list)  # FastAPI验证错误格式


@pytest.mark.integration
class TestAPIWorkflow:
    """测试API工作流程"""

    def test_basic_api_workflow(self, test_client, override_dependencies):
        """测试基本API工作流程"""
        # 1. 检查应用健康状态
        health_response = test_client.get("/health")
        assert health_response.status_code == 200

        # 2. 获取根信息
        root_response = test_client.get("/")
        assert root_response.status_code == 200

        # 3. 尝试获取预签名URL
        with patch('app.api.files.settings') as mock_settings:
            mock_settings.get_allowed_image_extensions.return_value = ['jpg', 'png']
            mock_settings.get_allowed_video_extensions.return_value = ['mp4']

            presign_response = test_client.post("/api/files/presign",
                                              params={
                                                  "filename": "workflow_test.jpg",
                                                  "file_type": "image/jpeg"
                                              })

            # 工作流应该能够完成，不管具体结果如何
            assert presign_response.status_code in [200, 400, 422, 500]

    def test_api_response_consistency(self, test_client):
        """测试API响应一致性"""
        # 多次调用同一端点，验证响应一致性
        responses = []

        for _ in range(3):
            response = test_client.get("/health")
            responses.append(response.json())

        # 所有响应应该相同（除了可能的时间戳）
        first_response = responses[0]
        for response in responses[1:]:
            assert response["status"] == first_response["status"]
            assert response["app_name"] == first_response["app_name"]
            assert response["version"] == first_response["version"]

    def test_concurrent_api_requests(self, test_client):
        """测试并发API请求"""
        import threading
        import queue

        results = queue.Queue()

        def make_request():
            try:
                response = test_client.get("/health")
                results.put(("success", response.status_code))
            except Exception as e:
                results.put(("error", str(e)))

        # 创建多个并发请求
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证结果
        success_count = 0
        while not results.empty():
            result_type, result_value = results.get()
            if result_type == "success":
                success_count += 1
                assert result_value == 200

        # 至少大部分请求应该成功
        assert success_count >= 3


@pytest.mark.integration
class TestAPIPerformance:
    """测试API性能"""

    def test_health_endpoint_performance(self, test_client):
        """测试健康检查端点性能"""
        # 预热
        test_client.get("/health")

        # 性能测试
        start_time = time.time()
        iterations = 10

        for _ in range(iterations):
            response = test_client.get("/health")
            assert response.status_code == 200

        end_time = time.time()
        avg_time = (end_time - start_time) / iterations

        # 健康检查应该很快（通常小于100ms）
        assert avg_time < 0.1, f"Health check too slow: {avg_time:.3f}s"

    def test_root_endpoint_performance(self, test_client):
        """测试根端点性能"""
        # 预热
        test_client.get("/")

        # 性能测试
        start_time = time.time()
        iterations = 10

        for _ in range(iterations):
            response = test_client.get("/")
            assert response.status_code == 200

        end_time = time.time()
        avg_time = (end_time - start_time) / iterations

        # 根端点应该很快
        assert avg_time < 0.1, f"Root endpoint too slow: {avg_time:.3f}s"

    def test_api_response_size(self, test_client):
        """测试API响应大小"""
        # 健康检查响应
        health_response = test_client.get("/health")
        health_size = len(health_response.content)

        # 根端点响应
        root_response = test_client.get("/")
        root_size = len(root_response.content)

        # 响应大小应该合理（不超过1KB）
        assert health_size < 1024, f"Health response too large: {health_size} bytes"
        assert root_size < 1024, f"Root response too large: {root_size} bytes"


@pytest.mark.integration
class TestAPIDocumentation:
    """测试API文档功能"""

    def test_openapi_schema_structure(self, test_client):
        """测试OpenAPI模式结构"""
        with patch.object(settings, 'DEBUG', True):
            response = test_client.get("/openapi.json")

            if response.status_code == 200:
                schema = response.json()

                # 验证OpenAPI模式基本结构
                assert "openapi" in schema
                assert "info" in schema
                assert "paths" in schema

                # 验证应用信息
                info = schema["info"]
                assert "title" in info
                assert "version" in info

                # 验证路径信息
                paths = schema["paths"]
                assert "/" in paths  # 根路径
                assert "/health" in paths  # 健康检查

    def test_api_tags_and_descriptions(self, test_client):
        """测试API标签和描述"""
        with patch.object(settings, 'DEBUG', True):
            response = test_client.get("/openapi.json")

            if response.status_code == 200:
                schema = response.json()
                paths = schema.get("paths", {})

                # 检查端点是否有适当的标签
                if "/health" in paths:
                    health_endpoint = paths["/health"]
                    if "get" in health_endpoint:
                        get_method = health_endpoint["get"]
                        assert "tags" in get_method
                        assert "Health" in get_method["tags"]


@pytest.mark.integration
class TestAPISummary:
    """API测试总结"""

    def test_comprehensive_api_functionality(self, test_client, override_dependencies):
        """综合API功能测试"""
        results = {}

        # 1. 基础端点测试
        root_response = test_client.get("/")
        results["root_endpoint"] = root_response.status_code == 200

        health_response = test_client.get("/health")
        results["health_endpoint"] = health_response.status_code == 200

        # 2. 文件API测试
        with patch('app.api.files.settings') as mock_settings:
            mock_settings.get_allowed_image_extensions.return_value = ['jpg']
            mock_settings.get_allowed_video_extensions.return_value = ['mp4']

            presign_response = test_client.post("/api/files/presign",
                                              params={
                                                  "filename": "test.jpg",
                                                  "file_type": "image/jpeg"
                                              })
            results["files_api"] = presign_response.status_code in [200, 400, 422]

        # 3. 错误处理测试
        error_response = test_client.get("/nonexistent")
        results["error_handling"] = error_response.status_code == 404

        # 4. 中间件测试
        process_time_header = "X-Process-Time" in health_response.headers
        results["middleware"] = process_time_header

        # 验证所有核心功能
        assert results["root_endpoint"], "Root endpoint failed"
        assert results["health_endpoint"], "Health endpoint failed"
        assert results["files_api"], "Files API failed"
        assert results["error_handling"], "Error handling failed"
        assert results["middleware"], "Middleware failed"

        # 打印测试报告
        print("\n=== API Comprehensive Test Results ===")
        for test_name, passed in results.items():
            status = "PASSED" if passed else "FAILED"
            print(f"{test_name}: {status}")
        print("======================================")

        return results
