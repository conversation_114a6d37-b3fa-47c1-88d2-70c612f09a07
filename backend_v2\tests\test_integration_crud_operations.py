"""
CRUD操作集成测试
测试数据库的增删改查功能，包括基础CRUD和业务特定的CRUD操作
"""
import pytest
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.core.database import Base
from app.models.file import File, FileType, FileStatus
from app.crud.crud_file import CRUDFile
from app.schemas.file import FileCreate, FileUpdate


# 创建测试专用的数据库引擎和会话
@pytest.fixture(scope="module")
def test_db_engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        "sqlite:///:memory:",
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # 清理
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_session(test_db_engine):
    """创建测试会话"""
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_db_engine,
        expire_on_commit=False
    )
    
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def crud_file():
    """文件CRUD操作实例"""
    return CRUDFile(File)





@pytest.fixture
def sample_file_data():
    """示例文件数据"""
    return {
        "filename": "test_image.jpg",
        "original_filename": "original_test.jpg",
        "file_type": FileType.IMAGE,
        "content_type": "image/jpeg",
        "storage_path": "uploads/images/2025/01/05/test_image.jpg",
        "storage_bucket": "clover-files",
        "file_size": 1024,
        "status": FileStatus.UPLOADED
    }


@pytest.mark.integration
class TestBasicCRUDOperations:
    """测试基础CRUD操作"""
    
    def test_create_and_get_batch(self, test_session, crud_batch, sample_batch_data):
        """测试创建和获取批次"""
        # 创建批次
        batch_create = BatchCreate(**sample_batch_data)
        created_batch = crud_batch.create(test_session, obj_in=batch_create)
        
        # 验证创建结果
        assert created_batch.id is not None
        assert created_batch.name == sample_batch_data["name"]
        assert created_batch.description == sample_batch_data["description"]
        assert created_batch.total_files == 0
        assert created_batch.processed_files == 0
        assert created_batch.created_at is not None
        assert created_batch.updated_at is not None
        
        # 通过ID获取批次
        retrieved_batch = crud_batch.get(test_session, id=created_batch.id)
        assert retrieved_batch is not None
        assert retrieved_batch.id == created_batch.id
        assert retrieved_batch.name == created_batch.name
    
    def test_create_and_get_file(self, test_session, crud_file, sample_file_data):
        """测试创建和获取文件"""
        # 创建文件
        file_create = FileCreate(**sample_file_data)
        created_file = crud_file.create(test_session, obj_in=file_create)
        
        # 验证创建结果
        assert created_file.id is not None
        assert created_file.filename == sample_file_data["filename"]
        assert created_file.file_type == FileType.IMAGE
        assert created_file.status == FileStatus.UPLOADED
        assert created_file.file_size == 1024
        assert created_file.created_at is not None
        
        # 通过ID获取文件
        retrieved_file = crud_file.get(test_session, id=created_file.id)
        assert retrieved_file is not None
        assert retrieved_file.id == created_file.id
        assert retrieved_file.filename == created_file.filename
    
    def test_update_operations(self, test_session, crud_file, sample_file_data):
        """测试更新操作"""
        # 创建文件
        file_create = FileCreate(**sample_file_data)
        created_file = crud_file.create(test_session, obj_in=file_create)
        
        # 更新文件
        update_data = FileUpdate(
            status=FileStatus.PROCESSED,
            file_size=2048
        )
        updated_file = crud_file.update(
            test_session, 
            db_obj=created_file, 
            obj_in=update_data
        )
        
        # 验证更新结果
        assert updated_file.id == created_file.id
        assert updated_file.status == FileStatus.PROCESSED
        assert updated_file.file_size == 2048
        assert updated_file.filename == created_file.filename  # 未更新的字段保持不变
    
    def test_delete_operations(self, test_session, crud_file, sample_file_data):
        """测试删除操作"""
        # 创建文件
        file_create = FileCreate(**sample_file_data)
        created_file = crud_file.create(test_session, obj_in=file_create)
        file_id = created_file.id
        
        # 删除文件
        deleted_file = crud_file.remove(test_session, id=file_id)
        
        # 验证删除结果
        assert deleted_file.id == file_id
        
        # 验证文件已被删除
        retrieved_file = crud_file.get(test_session, id=file_id)
        assert retrieved_file is None
    
    def test_get_multi_operations(self, test_session, crud_file, sample_file_data):
        """测试批量获取操作"""
        # 创建多个文件
        files = []
        for i in range(5):
            file_data = sample_file_data.copy()
            file_data["filename"] = f"test_file_{i}.jpg"
            file_create = FileCreate(**file_data)
            created_file = crud_file.create(test_session, obj_in=file_create)
            files.append(created_file)
        
        # 获取所有文件
        all_files = crud_file.get_multi(test_session)
        assert len(all_files) == 5
        
        # 测试分页
        first_page = crud_file.get_multi(test_session, skip=0, limit=2)
        assert len(first_page) == 2
        
        second_page = crud_file.get_multi(test_session, skip=2, limit=2)
        assert len(second_page) == 2
        
        third_page = crud_file.get_multi(test_session, skip=4, limit=2)
        assert len(third_page) == 1


@pytest.mark.integration
class TestBusinessSpecificCRUD:
    """测试业务特定的CRUD操作"""
    
    def test_get_files_by_batch_id(self, test_session, crud_file, crud_batch, sample_batch_data, sample_file_data):
        """测试根据批次ID获取文件"""
        # 创建批次
        batch_create = BatchCreate(**sample_batch_data)
        created_batch = crud_batch.create(test_session, obj_in=batch_create)
        
        # 创建属于该批次的文件
        for i in range(3):
            file_data = sample_file_data.copy()
            file_data["filename"] = f"batch_file_{i}.jpg"
            file_data["batch_id"] = created_batch.id
            file_create = FileCreate(**file_data)
            crud_file.create(test_session, obj_in=file_create)
        
        # 创建不属于该批次的文件
        other_file_data = sample_file_data.copy()
        other_file_data["filename"] = "other_file.jpg"
        # batch_id为None
        other_file_create = FileCreate(**other_file_data)
        crud_file.create(test_session, obj_in=other_file_create)
        
        # 获取批次文件
        batch_files = crud_file.get_by_batch_id(test_session, batch_id=created_batch.id)
        assert len(batch_files) == 3
        
        for file in batch_files:
            assert file.batch_id == created_batch.id
            assert file.filename.startswith("batch_file_")
    
    def test_get_file_by_filename(self, test_session, crud_file, sample_file_data):
        """测试根据文件名获取文件"""
        # 创建文件
        file_create = FileCreate(**sample_file_data)
        created_file = crud_file.create(test_session, obj_in=file_create)
        
        # 根据文件名查找
        found_file = crud_file.get_by_filename(test_session, filename=sample_file_data["filename"])
        assert found_file is not None
        assert found_file.id == created_file.id
        assert found_file.filename == sample_file_data["filename"]
        
        # 查找不存在的文件
        not_found = crud_file.get_by_filename(test_session, filename="nonexistent.jpg")
        assert not_found is None
    
    def test_get_files_by_type(self, test_session, crud_file, sample_file_data):
        """测试根据文件类型获取文件"""
        # 创建不同类型的文件
        image_data = sample_file_data.copy()
        image_data["filename"] = "image.jpg"
        image_data["file_type"] = FileType.IMAGE
        image_create = FileCreate(**image_data)
        crud_file.create(test_session, obj_in=image_create)
        
        video_data = sample_file_data.copy()
        video_data["filename"] = "video.mp4"
        video_data["file_type"] = FileType.VIDEO
        video_data["content_type"] = "video/mp4"
        video_create = FileCreate(**video_data)
        crud_file.create(test_session, obj_in=video_create)
        
        # 获取图像文件
        image_files = crud_file.get_by_type(test_session, file_type=FileType.IMAGE)
        assert len(image_files) == 1
        assert image_files[0].file_type == FileType.IMAGE
        assert image_files[0].filename == "image.jpg"
        
        # 获取视频文件
        video_files = crud_file.get_by_type(test_session, file_type=FileType.VIDEO)
        assert len(video_files) == 1
        assert video_files[0].file_type == FileType.VIDEO
        assert video_files[0].filename == "video.mp4"
    
    def test_get_files_by_status(self, test_session, crud_file, sample_file_data):
        """测试根据文件状态获取文件"""
        # 创建不同状态的文件
        uploaded_data = sample_file_data.copy()
        uploaded_data["filename"] = "uploaded.jpg"
        uploaded_data["status"] = FileStatus.UPLOADED
        uploaded_create = FileCreate(**uploaded_data)
        crud_file.create(test_session, obj_in=uploaded_create)
        
        processed_data = sample_file_data.copy()
        processed_data["filename"] = "processed.jpg"
        processed_data["status"] = FileStatus.PROCESSED
        processed_create = FileCreate(**processed_data)
        crud_file.create(test_session, obj_in=processed_create)
        
        # 获取已上传的文件
        uploaded_files = crud_file.get_by_status(test_session, status=FileStatus.UPLOADED)
        assert len(uploaded_files) == 1
        assert uploaded_files[0].status == FileStatus.UPLOADED
        
        # 获取已处理的文件
        processed_files = crud_file.get_by_status(test_session, status=FileStatus.PROCESSED)
        assert len(processed_files) == 1
        assert processed_files[0].status == FileStatus.PROCESSED


@pytest.mark.integration
class TestCRUDErrorHandling:
    """测试CRUD错误处理"""
    
    def test_get_nonexistent_record(self, test_session, crud_file):
        """测试获取不存在的记录"""
        # 获取不存在的文件
        nonexistent_file = crud_file.get(test_session, id=99999)
        assert nonexistent_file is None
    
    def test_update_nonexistent_record(self, test_session, crud_file):
        """测试更新不存在的记录"""
        # 创建一个假的文件对象
        fake_file = File(id=99999, filename="fake.jpg")
        
        # 尝试更新（这应该不会抛出异常，但也不会有实际效果）
        update_data = FileUpdate(status=FileStatus.PROCESSED)
        
        # 注意：这个测试可能需要根据实际的update实现调整
        # 如果update方法会检查对象是否存在于数据库中，可能会抛出异常
        try:
            updated_file = crud_file.update(test_session, db_obj=fake_file, obj_in=update_data)
            # 如果没有抛出异常，验证更新结果
            assert updated_file.id == 99999
        except Exception:
            # 如果抛出异常，这也是合理的行为
            pass
    
    def test_delete_nonexistent_record(self, test_session, crud_file):
        """测试删除不存在的记录"""
        # 尝试删除不存在的文件
        deleted_file = crud_file.remove(test_session, id=99999)
        assert deleted_file is None
