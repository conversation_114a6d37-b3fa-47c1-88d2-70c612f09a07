"""
MinIO文件操作集成测试
测试MinIO对象存储的实际操作，包括文件上传、下载、删除等
"""
import pytest
import io
import os
import time
import uuid
from typing import List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import timedelta

from app.services.minio_service import get_minio_service
from app.utils.storage_path import StoragePathManager, StorageType
from app.core.config import settings


# 测试配置
TEST_PREFIX = "test_integration"
TEST_BUCKET_PREFIX = "test-"


@pytest.fixture(scope="module")
def minio_service():
    """创建MinIO服务实例"""
    service = get_minio_service()
    return service


@pytest.fixture(scope="module")
def minio_available(minio_service):
    """检查MinIO是否可用"""
    try:
        return minio_service.check_connection()
    except Exception:
        return False


@pytest.fixture(scope="function")
def test_object_name():
    """生成唯一的测试对象名称"""
    return f"{TEST_PREFIX}/{uuid.uuid4().hex}/test_file.txt"


@pytest.fixture(scope="function")
def test_file_content():
    """生成测试文件内容"""
    return b"This is a test file content for MinIO integration testing."


@pytest.fixture(scope="function")
def cleanup_objects(minio_service):
    """测试后清理对象"""
    created_objects = []
    
    def add_object(object_name: str):
        created_objects.append(object_name)
    
    yield add_object
    
    # 清理创建的对象
    for object_name in created_objects:
        try:
            minio_service.delete_file(object_name)
        except Exception:
            pass  # 忽略清理错误


@pytest.mark.integration
class TestMinIOConnection:
    """测试MinIO连接和初始化"""
    
    def test_minio_service_initialization(self, minio_service):
        """测试MinIO服务初始化"""
        assert minio_service is not None
        assert hasattr(minio_service, 'client')
        assert hasattr(minio_service, 'bucket_name')
        assert minio_service.bucket_name == settings.MINIO_BUCKET_NAME
    
    def test_minio_connection_check(self, minio_service, minio_available):
        """测试MinIO连接检查"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        # 测试连接检查
        is_connected = minio_service.check_connection()
        assert is_connected is True
    
    def test_minio_initialization(self, minio_service, minio_available):
        """测试MinIO初始化"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        # 测试初始化
        initialized = minio_service.initialize()
        assert initialized is True
        assert minio_service._initialized is True
    
    def test_minio_client_info(self, minio_service, minio_available):
        """测试获取MinIO客户端信息"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        client_info = minio_service.get_client_info()
        assert isinstance(client_info, dict)
        
        if "error" not in client_info:
            # 验证信息结构
            assert "endpoint" in client_info
            assert "secure" in client_info
            assert "default_bucket" in client_info
            assert "buckets" in client_info
            assert "bucket_exists" in client_info
            
            assert client_info["endpoint"] == settings.MINIO_ENDPOINT
            assert client_info["default_bucket"] == settings.MINIO_BUCKET_NAME


@pytest.mark.integration
class TestMinIOFileOperations:
    """测试MinIO文件操作"""
    
    def test_file_upload_and_download(self, minio_service, minio_available, 
                                    test_object_name, test_file_content, cleanup_objects):
        """测试文件上传和下载"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        cleanup_objects(test_object_name)
        
        # 初始化服务
        assert minio_service.initialize()
        
        # 上传文件
        file_data = io.BytesIO(test_file_content)
        upload_success = minio_service.upload_file(
            file_data=file_data,
            object_name=test_object_name,
            content_type="text/plain",
            metadata={"test": "true", "purpose": "integration_test"}
        )
        assert upload_success is True
        
        # 验证文件存在
        assert minio_service.file_exists(test_object_name) is True
        
        # 下载文件
        downloaded_data = minio_service.download_file(test_object_name)
        assert downloaded_data is not None
        assert downloaded_data == test_file_content
    
    def test_file_info_retrieval(self, minio_service, minio_available,
                               test_object_name, test_file_content, cleanup_objects):
        """测试文件信息获取"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        cleanup_objects(test_object_name)
        
        # 上传文件
        file_data = io.BytesIO(test_file_content)
        minio_service.upload_file(
            file_data=file_data,
            object_name=test_object_name,
            content_type="text/plain",
            metadata={"test": "true", "author": "integration_test"}
        )
        
        # 获取文件信息
        file_info = minio_service.get_file_info(test_object_name)
        assert file_info is not None
        assert isinstance(file_info, dict)
        
        # 验证文件信息
        assert file_info["size"] == len(test_file_content)
        assert file_info["content_type"] == "text/plain"
        assert "last_modified" in file_info
        assert "etag" in file_info
        
        # 验证元数据（MinIO在HTTP头中返回，格式为x-amz-meta-*）
        metadata = file_info.get("metadata", {})
        # 元数据可能以不同格式返回，检查是否存在
        if metadata:
            # 可能的格式：直接键值对或x-amz-meta-前缀
            test_value = metadata.get("test") or metadata.get("x-amz-meta-test")
            author_value = metadata.get("author") or metadata.get("x-amz-meta-author")

            # 至少应该有一个元数据字段
            assert test_value is not None or author_value is not None, f"No metadata found: {metadata}"
    
    def test_file_deletion(self, minio_service, minio_available,
                          test_object_name, test_file_content):
        """测试文件删除"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        # 上传文件
        file_data = io.BytesIO(test_file_content)
        minio_service.upload_file(
            file_data=file_data,
            object_name=test_object_name,
            content_type="text/plain"
        )
        
        # 验证文件存在
        assert minio_service.file_exists(test_object_name) is True
        
        # 删除文件
        delete_success = minio_service.delete_file(test_object_name)
        assert delete_success is True
        
        # 验证文件不存在
        assert minio_service.file_exists(test_object_name) is False
    
    def test_file_list_operations(self, minio_service, minio_available, cleanup_objects):
        """测试文件列表操作"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        # 创建多个测试文件
        test_prefix = f"{TEST_PREFIX}/{uuid.uuid4().hex}"
        test_files = []
        
        for i in range(3):
            object_name = f"{test_prefix}/file_{i}.txt"
            test_files.append(object_name)
            cleanup_objects(object_name)
            
            file_data = io.BytesIO(f"Test file {i} content".encode())
            minio_service.upload_file(
                file_data=file_data,
                object_name=object_name,
                content_type="text/plain"
            )
        
        # 列出文件
        files = minio_service.list_directory(prefix=test_prefix, recursive=True)
        assert isinstance(files, list)

        # 验证文件数量（可能包含目录结构）
        # 获取实际的文件对象名称
        object_names = [f.get("object_name", "") for f in files]

        # 验证我们上传的文件都在列表中
        found_files = 0
        for test_file in test_files:
            if test_file in object_names:
                found_files += 1

        assert found_files >= 3, f"Expected at least 3 files, found {found_files}. Files: {object_names}"


@pytest.mark.integration
class TestMinIOBatchOperations:
    """测试MinIO批量操作"""
    
    def test_multiple_file_upload_and_delete(self, minio_service, minio_available):
        """测试多文件上传和批量删除"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        # 创建多个测试文件
        test_prefix = f"{TEST_PREFIX}/{uuid.uuid4().hex}"
        test_files = []
        
        for i in range(5):
            object_name = f"{test_prefix}/batch_file_{i}.txt"
            test_files.append(object_name)
            
            file_data = io.BytesIO(f"Batch test file {i} content".encode())
            upload_success = minio_service.upload_file(
                file_data=file_data,
                object_name=object_name,
                content_type="text/plain"
            )
            assert upload_success is True
        
        # 验证所有文件都存在
        for object_name in test_files:
            assert minio_service.file_exists(object_name) is True
        
        # 逐个删除文件（模拟批量删除）
        delete_results = []
        for object_name in test_files:
            delete_success = minio_service.delete_file(object_name)
            delete_results.append(delete_success)

        # 验证所有删除操作都成功
        assert all(delete_results), f"Some deletions failed: {delete_results}"

        # 验证所有文件都已删除
        for object_name in test_files:
            assert minio_service.file_exists(object_name) is False
    
    def test_concurrent_file_operations(self, minio_service, minio_available):
        """测试并发文件操作"""
        if not minio_available:
            pytest.skip("MinIO service not available")
        
        test_prefix = f"{TEST_PREFIX}/{uuid.uuid4().hex}"
        results = []
        errors = []
        
        def upload_file(file_id):
            try:
                object_name = f"{test_prefix}/concurrent_file_{file_id}.txt"
                file_data = io.BytesIO(f"Concurrent test file {file_id}".encode())
                
                success = minio_service.upload_file(
                    file_data=file_data,
                    object_name=object_name,
                    content_type="text/plain"
                )
                
                if success:
                    results.append(object_name)
                return object_name
            except Exception as e:
                errors.append(f"File {file_id}: {str(e)}")
                return None
        
        # 并发上传文件
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(upload_file, i) for i in range(10)]
            uploaded_files = [future.result() for future in as_completed(futures)]
        
        # 验证结果
        assert len(errors) == 0, f"Concurrent upload errors: {errors}"
        successful_uploads = [f for f in uploaded_files if f is not None]
        assert len(successful_uploads) == 10
        
        # 清理文件
        if successful_uploads:
            for object_name in successful_uploads:
                minio_service.delete_file(object_name)


@pytest.mark.integration
class TestMinIOPresignedURLs:
    """测试MinIO预签名URL功能"""

    def test_generate_presigned_upload_url(self, minio_service, minio_available, test_object_name):
        """测试生成预签名上传URL"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        # 生成预签名上传URL
        upload_url = minio_service.generate_presigned_url(
            object_name=test_object_name,
            method="PUT",
            expires=timedelta(hours=1)
        )

        if upload_url:
            assert isinstance(upload_url, str)
            assert upload_url.startswith("http")
            assert test_object_name in upload_url
        else:
            pytest.skip("Presigned URL generation not supported or failed")

    def test_generate_presigned_download_url(self, minio_service, minio_available,
                                           test_object_name, test_file_content, cleanup_objects):
        """测试生成预签名下载URL"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        cleanup_objects(test_object_name)

        # 先上传文件
        file_data = io.BytesIO(test_file_content)
        minio_service.upload_file(
            file_data=file_data,
            object_name=test_object_name,
            content_type="text/plain"
        )

        # 生成预签名下载URL
        download_url = minio_service.generate_presigned_url(
            object_name=test_object_name,
            method="GET",
            expires=timedelta(hours=1)
        )

        if download_url:
            assert isinstance(download_url, str)
            assert download_url.startswith("http")
            assert test_object_name in download_url
        else:
            pytest.skip("Presigned URL generation not supported or failed")

    def test_get_file_url(self, minio_service, minio_available,
                         test_object_name, test_file_content, cleanup_objects):
        """测试获取文件访问URL"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        cleanup_objects(test_object_name)

        # 上传文件
        file_data = io.BytesIO(test_file_content)
        minio_service.upload_file(
            file_data=file_data,
            object_name=test_object_name,
            content_type="text/plain"
        )

        # 获取文件URL
        file_url = minio_service.get_file_url(test_object_name)

        if file_url:
            assert isinstance(file_url, str)
            assert file_url.startswith("http")
            assert test_object_name in file_url


@pytest.mark.integration
class TestMinIOErrorHandling:
    """测试MinIO错误处理"""

    def test_download_nonexistent_file(self, minio_service, minio_available):
        """测试下载不存在的文件"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        nonexistent_file = f"{TEST_PREFIX}/nonexistent_{uuid.uuid4().hex}.txt"

        # 尝试下载不存在的文件
        downloaded_data = minio_service.download_file(nonexistent_file)
        assert downloaded_data is None

    def test_delete_nonexistent_file(self, minio_service, minio_available):
        """测试删除不存在的文件"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        nonexistent_file = f"{TEST_PREFIX}/nonexistent_{uuid.uuid4().hex}.txt"

        # 尝试删除不存在的文件
        delete_success = minio_service.delete_file(nonexistent_file)
        # 删除不存在的文件通常返回True（幂等操作）
        assert isinstance(delete_success, bool)

    def test_file_exists_nonexistent_file(self, minio_service, minio_available):
        """测试检查不存在文件的存在性"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        nonexistent_file = f"{TEST_PREFIX}/nonexistent_{uuid.uuid4().hex}.txt"

        # 检查不存在的文件
        exists = minio_service.file_exists(nonexistent_file)
        assert exists is False

    def test_get_info_nonexistent_file(self, minio_service, minio_available):
        """测试获取不存在文件的信息"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        nonexistent_file = f"{TEST_PREFIX}/nonexistent_{uuid.uuid4().hex}.txt"

        # 获取不存在文件的信息
        file_info = minio_service.get_file_info(nonexistent_file)
        assert file_info is None

    def test_upload_empty_file(self, minio_service, minio_available, cleanup_objects):
        """测试上传空文件"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        test_object_name = f"{TEST_PREFIX}/empty_{uuid.uuid4().hex}.txt"
        cleanup_objects(test_object_name)

        # 上传空文件
        empty_data = io.BytesIO(b"")
        upload_success = minio_service.upload_file(
            file_data=empty_data,
            object_name=test_object_name,
            content_type="text/plain"
        )

        assert upload_success is True

        # 验证文件存在且大小为0
        assert minio_service.file_exists(test_object_name) is True

        file_info = minio_service.get_file_info(test_object_name)
        if file_info:
            assert file_info["size"] == 0


@pytest.mark.integration
class TestMinIOPerformance:
    """测试MinIO性能"""

    def test_large_file_upload_download(self, minio_service, minio_available, cleanup_objects):
        """测试大文件上传和下载"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        test_object_name = f"{TEST_PREFIX}/large_file_{uuid.uuid4().hex}.bin"
        cleanup_objects(test_object_name)

        # 创建1MB的测试文件
        large_content = b"A" * (1024 * 1024)  # 1MB
        file_data = io.BytesIO(large_content)

        # 测试上传性能
        start_time = time.time()
        upload_success = minio_service.upload_file(
            file_data=file_data,
            object_name=test_object_name,
            content_type="application/octet-stream"
        )
        upload_time = time.time() - start_time

        assert upload_success is True
        assert upload_time < 30, f"Large file upload too slow: {upload_time:.2f}s"

        # 测试下载性能
        start_time = time.time()
        downloaded_data = minio_service.download_file(test_object_name)
        download_time = time.time() - start_time

        assert downloaded_data is not None
        assert len(downloaded_data) == len(large_content)
        assert download_time < 30, f"Large file download too slow: {download_time:.2f}s"

    def test_multiple_small_files_performance(self, minio_service, minio_available):
        """测试多个小文件的性能"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        test_prefix = f"{TEST_PREFIX}/perf_{uuid.uuid4().hex}"
        file_count = 20
        test_files = []

        # 测试批量上传性能
        start_time = time.time()

        for i in range(file_count):
            object_name = f"{test_prefix}/small_file_{i}.txt"
            test_files.append(object_name)

            file_data = io.BytesIO(f"Small file {i} content".encode())
            upload_success = minio_service.upload_file(
                file_data=file_data,
                object_name=object_name,
                content_type="text/plain"
            )
            assert upload_success is True

        upload_time = time.time() - start_time
        avg_upload_time = upload_time / file_count

        assert avg_upload_time < 1.0, f"Average upload time too slow: {avg_upload_time:.3f}s"

        # 清理文件
        for object_name in test_files:
            minio_service.delete_file(object_name)


@pytest.mark.integration
class TestMinIOStorageIntegration:
    """测试MinIO与存储路径管理的集成"""

    def test_storage_path_integration(self, minio_service, minio_available, cleanup_objects):
        """测试与StoragePathManager的集成"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        # 创建存储路径管理器
        storage_manager = StoragePathManager()

        # 生成不同类型的存储路径
        test_cases = [
            (StorageType.ORIGINAL, "test.jpg"),
            (StorageType.PROCESSED, "test_processed.jpg"),
            (StorageType.THUMBNAIL, "test_thumb.jpg"),
            (StorageType.TEMP, "test_temp.jpg")
        ]

        for storage_type, filename in test_cases:
            # 生成存储路径
            storage_path = storage_manager.generate_path(
                storage_type=storage_type,
                filename=filename,
                task_id="test_task_123",
                batch_id="test_batch_456"
            )

            cleanup_objects(storage_path)

            # 上传文件到生成的路径
            test_content = f"Test content for {storage_type.value}".encode()
            file_data = io.BytesIO(test_content)

            upload_success = minio_service.upload_file(
                file_data=file_data,
                object_name=storage_path,
                content_type="image/jpeg",
                metadata={
                    "storage_type": storage_type.value,
                    "original_filename": filename,
                    "task_id": "test_task_123",
                    "batch_id": "test_batch_456"
                }
            )

            assert upload_success is True

            # 验证文件存在
            assert minio_service.file_exists(storage_path) is True

            # 验证文件信息
            file_info = minio_service.get_file_info(storage_path)
            assert file_info is not None

            metadata = file_info.get("metadata", {})
            assert metadata.get("storage_type") == storage_type.value
            assert metadata.get("original_filename") == filename

    def test_path_validation_with_minio(self, minio_service, minio_available, cleanup_objects):
        """测试路径验证与MinIO的集成"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        storage_manager = StoragePathManager()

        # 测试有效路径
        valid_path = storage_manager.generate_path(
            storage_type=StorageType.ORIGINAL,
            filename="valid_test.jpg",
            task_id="task_123",
            batch_id="batch_456"
        )

        cleanup_objects(valid_path)

        # 验证路径格式
        assert storage_manager.validate_path(valid_path) is True

        # 上传文件到有效路径
        file_data = io.BytesIO(b"Valid path test content")
        upload_success = minio_service.upload_file(
            file_data=file_data,
            object_name=valid_path,
            content_type="image/jpeg"
        )

        assert upload_success is True
        assert minio_service.file_exists(valid_path) is True


@pytest.mark.integration
class TestMinIOOperationsSummary:
    """MinIO操作测试总结"""

    def test_comprehensive_minio_workflow(self, minio_service, minio_available, cleanup_objects):
        """综合MinIO工作流程测试"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        # 1. 初始化服务
        assert minio_service.initialize() is True

        # 2. 检查连接
        assert minio_service.check_connection() is True

        # 3. 获取客户端信息
        client_info = minio_service.get_client_info()
        assert isinstance(client_info, dict)
        assert "error" not in client_info

        # 4. 创建测试文件
        test_prefix = f"{TEST_PREFIX}/workflow_{uuid.uuid4().hex}"
        test_files = []

        for i in range(3):
            object_name = f"{test_prefix}/workflow_file_{i}.txt"
            test_files.append(object_name)
            cleanup_objects(object_name)

            # 上传文件
            content = f"Workflow test file {i} content".encode()
            file_data = io.BytesIO(content)

            upload_success = minio_service.upload_file(
                file_data=file_data,
                object_name=object_name,
                content_type="text/plain",
                metadata={"workflow": "test", "file_index": str(i)}
            )
            assert upload_success is True

            # 验证文件存在
            assert minio_service.file_exists(object_name) is True

            # 获取文件信息
            file_info = minio_service.get_file_info(object_name)
            assert file_info is not None
            assert file_info["size"] == len(content)

            # 下载并验证内容
            downloaded_data = minio_service.download_file(object_name)
            assert downloaded_data == content

        # 5. 列出文件
        files = minio_service.list_directory(prefix=test_prefix)
        assert len(files) >= 3

        # 6. 生成预签名URL（如果支持）
        for object_name in test_files:
            url = minio_service.get_file_url(object_name)
            if url:
                assert isinstance(url, str)
                assert url.startswith("http")

        # 7. 逐个删除（模拟批量删除）
        delete_results = []
        for object_name in test_files:
            delete_success = minio_service.delete_file(object_name)
            delete_results.append(delete_success)

        assert all(delete_results), f"Some deletions failed: {delete_results}"

        # 8. 验证文件已删除
        for object_name in test_files:
            assert minio_service.file_exists(object_name) is False

        print("\n=== MinIO Comprehensive Workflow Test Results ===")
        print("✅ Service initialization: PASSED")
        print("✅ Connection check: PASSED")
        print("✅ Client info retrieval: PASSED")
        print("✅ File upload operations: PASSED")
        print("✅ File existence checks: PASSED")
        print("✅ File info retrieval: PASSED")
        print("✅ File download operations: PASSED")
        print("✅ File listing: PASSED")
        print("✅ URL generation: PASSED")
        print("✅ Batch deletion: PASSED")
        print("✅ Cleanup verification: PASSED")
        print("================================================")

    def test_minio_service_reliability(self, minio_service, minio_available):
        """测试MinIO服务可靠性"""
        if not minio_available:
            pytest.skip("MinIO service not available")

        # 多次连接测试
        for i in range(5):
            assert minio_service.check_connection() is True

        # 重复初始化测试
        for i in range(3):
            assert minio_service.initialize() is True

        # 客户端信息获取稳定性
        for i in range(3):
            client_info = minio_service.get_client_info()
            assert isinstance(client_info, dict)
            if "error" not in client_info:
                assert "endpoint" in client_info
                assert "default_bucket" in client_info

        print("\n=== MinIO Service Reliability Test Results ===")
        print("✅ Multiple connection checks: PASSED")
        print("✅ Repeated initialization: PASSED")
        print("✅ Client info stability: PASSED")
        print("===============================================")
