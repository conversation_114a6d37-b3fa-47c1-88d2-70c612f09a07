"""
系统整体集成功能验证测试
验证数据库、MinIO、API端点之间的完整集成功能
"""
import pytest
import json
import time
import uuid
import io
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.main import app
from app.core.database import get_db, SessionLocal, engine, Base
from app.services.minio_service import get_minio_service
from app.models import Task, File, Batch
from app.core.config import settings


@pytest.fixture(scope="module")
def test_client():
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture(scope="function")
def test_db():
    """创建测试数据库"""
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    # 创建数据库会话
    db = SessionLocal()
    
    yield db
    
    # 清理
    db.close()
    # 删除所有表
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def override_db_dependency(test_db):
    """覆盖数据库依赖"""
    def get_test_db():
        return test_db
    
    app.dependency_overrides[get_db] = get_test_db
    yield test_db
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def mock_minio_service():
    """Mock MinIO服务"""
    service = Mock()
    service.initialize.return_value = True
    service.check_connection.return_value = True
    service.upload_file.return_value = True
    service.download_file.return_value = b"test file content"
    service.delete_file.return_value = True
    service.file_exists.return_value = True
    service.get_file_info.return_value = {
        "size": 1024,
        "content_type": "image/jpeg",
        "last_modified": "2025-01-05T10:00:00Z",
        "metadata": {"test": "true"}
    }
    service.generate_presigned_put_url.return_value = "https://test-presigned-url"
    service.get_file_url.return_value = "https://test-file-url"
    
    def mock_get_minio_service():
        return service
    
    app.dependency_overrides[get_minio_service] = mock_get_minio_service
    yield service
    app.dependency_overrides.clear()


@pytest.mark.integration
class TestSystemHealthCheck:
    """测试系统健康检查"""
    
    def test_system_components_health(self, test_client, override_db_dependency, mock_minio_service):
        """测试系统各组件健康状态"""
        # 1. 测试API健康检查
        health_response = test_client.get("/health")
        assert health_response.status_code == 200
        health_data = health_response.json()
        assert health_data["status"] == "healthy"
        
        # 2. 测试数据库连接
        # 通过数据库依赖注入验证数据库连接正常
        assert override_db_dependency is not None
        
        # 3. 测试MinIO连接
        assert mock_minio_service.check_connection() is True
        
        # 4. 测试根端点
        root_response = test_client.get("/")
        assert root_response.status_code == 200
        root_data = root_response.json()
        assert "message" in root_data
        assert "version" in root_data
    
    def test_system_configuration_consistency(self, test_client):
        """测试系统配置一致性"""
        # 获取健康检查信息
        health_response = test_client.get("/health")
        health_data = health_response.json()
        
        # 获取根端点信息
        root_response = test_client.get("/")
        root_data = root_response.json()
        
        # 验证版本信息一致性
        assert health_data["version"] == root_data["version"]
        assert health_data["app_name"] == settings.APP_NAME
        assert health_data["version"] == settings.APP_VERSION


@pytest.mark.integration
class TestDatabaseMinIOIntegration:
    """测试数据库与MinIO集成"""
    
    def test_file_metadata_storage_integration(self, override_db_dependency, mock_minio_service):
        """测试文件元数据存储集成"""
        db = override_db_dependency
        
        # 1. 创建文件记录
        file_record = File(
            filename="test_integration.jpg",
            original_filename="test_integration.jpg",
            file_type="image",
            storage_path="test/path/stored_test_integration.jpg",
            file_size=1024,
            content_type="image/jpeg",
            status="uploaded"
        )
        
        db.add(file_record)
        db.commit()
        db.refresh(file_record)
        
        # 2. 验证数据库记录
        stored_file = db.query(File).filter(File.id == file_record.id).first()
        assert stored_file is not None
        assert stored_file.original_filename == "test_integration.jpg"
        assert stored_file.file_size == 1024
        
        # 3. 模拟MinIO文件存储
        mock_minio_service.upload_file.return_value = True
        mock_minio_service.file_exists.return_value = True
        
        # 验证文件在MinIO中存在
        assert mock_minio_service.file_exists(stored_file.storage_path) is True

        # 4. 获取文件信息
        file_info = mock_minio_service.get_file_info(stored_file.storage_path)
        assert file_info["size"] == stored_file.file_size
        assert file_info["content_type"] == stored_file.content_type
    
    def test_task_file_relationship_integration(self, override_db_dependency, mock_minio_service):
        """测试任务与文件关系集成"""
        db = override_db_dependency
        
        # 1. 创建任务
        task = Task(
            name="Integration Test Task",
            description="Testing task-file integration",
            status="pending"
        )
        db.add(task)
        db.commit()
        db.refresh(task)
        
        # 2. 创建关联文件
        file_record = File(
            batch_id=None,  # 暂时不关联批次
            filename="task_file.jpg",
            original_filename="task_file.jpg",
            file_type="image",
            storage_path=f"tasks/{task.id}/stored_task_file.jpg",
            file_size=2048,
            content_type="image/jpeg",
            status="uploaded"
        )
        db.add(file_record)
        db.commit()
        
        # 3. 验证关系
        stored_task = db.query(Task).filter(Task.id == task.id).first()
        # 注意：由于File模型没有task_id字段，我们通过其他方式验证
        stored_file = db.query(File).filter(File.id == file_record.id).first()

        assert stored_task is not None
        assert stored_file is not None
        assert stored_file.original_filename == "task_file.jpg"

        # 4. 验证MinIO存储
        assert mock_minio_service.file_exists(file_record.storage_path) is True


@pytest.mark.integration
class TestAPIEndToEndWorkflow:
    """测试API端到端工作流程"""
    
    def test_file_upload_workflow(self, test_client, override_db_dependency, mock_minio_service):
        """测试文件上传完整工作流程"""
        with patch('app.api.files.settings') as mock_settings:
            # 配置允许的文件类型
            mock_settings.get_allowed_image_extensions.return_value = ['jpg', 'png']
            mock_settings.get_allowed_video_extensions.return_value = ['mp4']
            
            # 1. 获取预签名上传URL
            presign_response = test_client.post("/api/files/presign", 
                                              params={
                                                  "filename": "workflow_test.jpg",
                                                  "file_type": "image/jpeg",
                                                  "expires_hours": 1
                                              })
            
            # 验证预签名URL响应
            assert presign_response.status_code in [200, 400, 422]
            
            if presign_response.status_code == 200:
                presign_data = presign_response.json()
                # 验证响应包含必要信息
                assert "upload_url" in presign_data or "presigned_url" in presign_data
    
    def test_health_check_workflow(self, test_client, override_db_dependency, mock_minio_service):
        """测试健康检查工作流程"""
        # 1. 检查API健康状态
        health_response = test_client.get("/health")
        assert health_response.status_code == 200
        
        health_data = health_response.json()
        assert health_data["status"] == "healthy"
        
        # 2. 验证数据库连接（通过依赖注入）
        db = override_db_dependency
        assert db is not None
        
        # 3. 验证MinIO连接
        assert mock_minio_service.check_connection() is True
        
        # 4. 验证系统整体状态
        assert health_data["app_name"] == settings.APP_NAME
        assert health_data["version"] == settings.APP_VERSION
    
    def test_error_handling_workflow(self, test_client, override_db_dependency, mock_minio_service):
        """测试错误处理工作流程"""
        # 1. 测试API错误处理
        error_response = test_client.get("/nonexistent-endpoint")
        assert error_response.status_code == 404
        
        # 2. 测试文件API错误处理
        invalid_file_response = test_client.post("/api/files/presign", 
                                                params={
                                                    "filename": "test.exe",  # 不允许的类型
                                                    "file_type": "application/exe"
                                                })
        # 应该返回错误或验证失败
        assert invalid_file_response.status_code in [400, 422]
        
        # 3. 测试参数验证错误
        missing_params_response = test_client.post("/api/files/presign")
        assert missing_params_response.status_code == 422


@pytest.mark.integration
class TestSystemPerformanceIntegration:
    """测试系统性能集成"""
    
    def test_concurrent_requests_performance(self, test_client, override_db_dependency, mock_minio_service):
        """测试并发请求性能"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_health_request():
            try:
                start_time = time.time()
                response = test_client.get("/health")
                end_time = time.time()
                results.put(("success", response.status_code, end_time - start_time))
            except Exception as e:
                results.put(("error", str(e), 0))
        
        # 创建并发请求
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_health_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 分析结果
        success_count = 0
        total_time = 0
        
        while not results.empty():
            result_type, status_or_error, response_time = results.get()
            if result_type == "success":
                success_count += 1
                total_time += response_time
                assert status_or_error == 200
        
        # 验证性能
        assert success_count >= 3  # 至少大部分请求成功
        if success_count > 0:
            avg_time = total_time / success_count
            assert avg_time < 1.0, f"Average response time too slow: {avg_time:.3f}s"
    
    def test_system_resource_usage(self, test_client, override_db_dependency, mock_minio_service):
        """测试系统资源使用"""
        # 连续发送多个请求，验证系统稳定性
        for i in range(10):
            health_response = test_client.get("/health")
            assert health_response.status_code == 200
            
            root_response = test_client.get("/")
            assert root_response.status_code == 200
            
            # 验证响应时间头部存在
            assert "X-Process-Time" in health_response.headers
            process_time = float(health_response.headers["X-Process-Time"])
            assert process_time < 1.0  # 处理时间应该合理


@pytest.mark.integration
class TestSystemReliabilityIntegration:
    """测试系统可靠性集成"""

    def test_component_failure_recovery(self, test_client, override_db_dependency):
        """测试组件故障恢复"""
        # 1. 正常状态测试
        health_response = test_client.get("/health")
        assert health_response.status_code == 200

        # 2. 模拟MinIO服务故障
        def mock_failing_minio_service():
            service = Mock()
            service.check_connection.return_value = False
            service.initialize.return_value = False
            return service

        app.dependency_overrides[get_minio_service] = mock_failing_minio_service

        # 3. 验证系统在MinIO故障时的行为
        # API应该仍然可以响应，但文件操作可能失败
        health_response_with_failure = test_client.get("/health")
        assert health_response_with_failure.status_code == 200

        # 清理依赖覆盖
        app.dependency_overrides.clear()

    def test_data_consistency_validation(self, override_db_dependency, mock_minio_service):
        """测试数据一致性验证"""
        db = override_db_dependency

        # 1. 创建文件记录
        file_record = File(
            filename="consistency_test.jpg",
            original_filename="consistency_test.jpg",
            file_type="image",
            storage_path="consistency/stored_consistency_test.jpg",
            file_size=1024,
            content_type="image/jpeg",
            status="uploaded"
        )

        db.add(file_record)
        db.commit()

        # 2. 验证数据库记录存在
        stored_file = db.query(File).filter(File.id == file_record.id).first()
        assert stored_file is not None

        # 3. 验证MinIO文件存在（模拟）
        mock_minio_service.file_exists.return_value = True
        assert mock_minio_service.file_exists(stored_file.storage_path) is True

        # 4. 验证文件信息一致性
        file_info = mock_minio_service.get_file_info(stored_file.storage_path)
        assert file_info["size"] == stored_file.file_size
        assert file_info["content_type"] == stored_file.content_type


@pytest.mark.integration
class TestSystemIntegrationSummary:
    """系统集成测试总结"""

    def test_comprehensive_system_integration(self, test_client, override_db_dependency, mock_minio_service):
        """综合系统集成测试"""
        results = {}

        # 1. 系统健康检查
        health_response = test_client.get("/health")
        results["system_health"] = health_response.status_code == 200

        # 2. 数据库集成
        db = override_db_dependency
        test_task = Task(
            name="Integration Summary Test",
            description="Testing comprehensive integration",
            status="pending"
        )
        db.add(test_task)
        db.commit()

        stored_task = db.query(Task).filter(Task.id == test_task.id).first()
        results["database_integration"] = stored_task is not None

        # 3. MinIO集成
        results["minio_integration"] = mock_minio_service.check_connection()

        # 4. API端点集成
        root_response = test_client.get("/")
        results["api_integration"] = root_response.status_code == 200

        # 5. 文件API集成
        with patch('app.api.files.settings') as mock_settings:
            mock_settings.get_allowed_image_extensions.return_value = ['jpg']
            mock_settings.get_allowed_video_extensions.return_value = ['mp4']

            presign_response = test_client.post("/api/files/presign",
                                              params={
                                                  "filename": "summary_test.jpg",
                                                  "file_type": "image/jpeg"
                                              })
            results["file_api_integration"] = presign_response.status_code in [200, 400, 422]

        # 6. 错误处理集成
        error_response = test_client.get("/nonexistent")
        results["error_handling_integration"] = error_response.status_code == 404

        # 7. 中间件集成
        middleware_working = "X-Process-Time" in health_response.headers
        results["middleware_integration"] = middleware_working

        # 验证所有集成功能
        assert results["system_health"], "System health check failed"
        assert results["database_integration"], "Database integration failed"
        assert results["minio_integration"], "MinIO integration failed"
        assert results["api_integration"], "API integration failed"
        assert results["file_api_integration"], "File API integration failed"
        assert results["error_handling_integration"], "Error handling integration failed"
        assert results["middleware_integration"], "Middleware integration failed"

        # 打印集成测试报告
        print("\n=== System Integration Test Results ===")
        for test_name, passed in results.items():
            status = "PASSED" if passed else "FAILED"
            print(f"{test_name}: {status}")
        print("=======================================")

        return results

    def test_system_integration_performance_summary(self, test_client, override_db_dependency, mock_minio_service):
        """系统集成性能总结测试"""
        performance_results = {}

        # 1. API响应时间测试
        start_time = time.time()
        for _ in range(5):
            response = test_client.get("/health")
            assert response.status_code == 200
        end_time = time.time()

        avg_api_time = (end_time - start_time) / 5
        performance_results["avg_api_response_time"] = avg_api_time

        # 2. 数据库操作性能测试
        db = override_db_dependency
        start_time = time.time()

        for i in range(3):
            task = Task(
                name=f"Performance Test Task {i}",
                description="Testing database performance",
                status="pending"
            )
            db.add(task)

        db.commit()
        end_time = time.time()

        avg_db_time = (end_time - start_time) / 3
        performance_results["avg_db_operation_time"] = avg_db_time

        # 3. MinIO操作性能测试
        start_time = time.time()
        for _ in range(3):
            mock_minio_service.check_connection()
            mock_minio_service.file_exists("test/path")
        end_time = time.time()

        avg_minio_time = (end_time - start_time) / 3
        performance_results["avg_minio_operation_time"] = avg_minio_time

        # 验证性能基准
        assert avg_api_time < 0.5, f"API response too slow: {avg_api_time:.3f}s"
        assert avg_db_time < 0.1, f"Database operations too slow: {avg_db_time:.3f}s"
        assert avg_minio_time < 0.1, f"MinIO operations too slow: {avg_minio_time:.3f}s"

        # 打印性能报告
        print("\n=== System Integration Performance Results ===")
        for metric, value in performance_results.items():
            print(f"{metric}: {value:.6f}s")
        print("==============================================")

        return performance_results
