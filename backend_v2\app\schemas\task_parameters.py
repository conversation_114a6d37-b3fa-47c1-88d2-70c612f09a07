"""
任务参数验证模式
"""
from typing import Dict, Any, Optional, Union, List
from pydantic import BaseModel, Field, validator, root_validator
from enum import Enum

from app.schemas.task_submit import TaskType


class ScaleModeType(str, Enum):
    """缩放模式枚举"""
    FIT = "fit"
    FILL = "fill"
    STRETCH = "stretch"
    CROP = "crop"
    RATIO = "ratio"
    CUSTOM = "custom"


class ThresholdType(str, Enum):
    """阈值类型枚举"""
    BINARY = "binary"
    BINARY_INV = "binary_inv"
    TRUNC = "trunc"
    TOZERO = "tozero"
    TOZERO_INV = "tozero_inv"


class FilterType(str, Enum):
    """滤镜类型枚举"""
    GAUSSIAN = "gaussian"
    MEDIAN = "median"
    BILATERAL = "bilateral"
    SHARPEN = "sharpen"


class EdgeType(str, Enum):
    """边缘检测类型枚举"""
    CANNY = "canny"
    SOBEL = "sobel"
    LAPLACIAN = "laplacian"


class TransformType(str, Enum):
    """变换类型枚举"""
    ROTATE_90 = "rotate_90"
    ROTATE_180 = "rotate_180"
    ROTATE_270 = "rotate_270"
    FLIP_HORIZONTAL = "flip_horizontal"
    FLIP_VERTICAL = "flip_vertical"
    FLIP_BOTH = "flip_both"
    ROTATE_CUSTOM = "rotate_custom"


class StitchingMode(str, Enum):
    """拼接模式枚举"""
    PANORAMA = "panorama"
    SCANS = "scans"


# 基础参数模型
class ImageSharpenParams(BaseModel):
    """图像锐化参数"""
    intensity: float = Field(default=1.0, ge=0.1, le=5.0, description="锐化强度")


class ImageGammaParams(BaseModel):
    """图像伽马校正参数"""
    gamma: float = Field(default=1.0, ge=0.1, le=3.0, description="伽马值")


class ImageGrayscaleParams(BaseModel):
    """图像灰度转换参数"""
    pass  # 无需额外参数


class ImageEdgeDetectionParams(BaseModel):
    """图像边缘检测参数"""
    low_threshold: int = Field(default=50, ge=0, le=255, description="低阈值")
    high_threshold: int = Field(default=150, ge=0, le=255, description="高阈值")
    
    @validator('high_threshold')
    def validate_thresholds(cls, v, values):
        """验证高阈值必须大于低阈值"""
        low_threshold = values.get('low_threshold', 0)
        if v <= low_threshold:
            raise ValueError("高阈值必须大于低阈值")
        return v


class ImageFusionParams(BaseModel):
    """图像融合参数"""
    weight1: float = Field(default=0.5, ge=0.0, le=1.0, description="第一张图像权重")
    weight2: float = Field(default=0.5, ge=0.0, le=1.0, description="第二张图像权重")
    
    @root_validator
    def validate_weights(cls, values):
        """验证权重之和为1"""
        weight1 = values.get('weight1', 0.5)
        weight2 = values.get('weight2', 0.5)
        if abs(weight1 + weight2 - 1.0) > 0.001:
            raise ValueError("两个权重之和必须等于1.0")
        return values


class BeautyEnhancementParams(BaseModel):
    """美颜处理参数"""
    slimming_strength: float = Field(default=0.3, ge=0.0, le=1.0, description="瘦脸强度")
    smoothing_strength: float = Field(default=0.5, ge=0.0, le=1.0, description="磨皮强度")


class ImageStitchingParams(BaseModel):
    """图像拼接参数"""
    mode: StitchingMode = Field(default=StitchingMode.PANORAMA, description="拼接模式")


class TextureTransferParams(BaseModel):
    """纹理转换参数"""
    block_size: int = Field(default=15, ge=5, le=50, description="块大小")
    alpha_start: float = Field(default=0.1, ge=0.0, le=1.0, description="起始透明度")
    alpha_end: float = Field(default=0.9, ge=0.0, le=1.0, description="结束透明度")
    
    @validator('alpha_end')
    def validate_alpha_range(cls, v, values):
        """验证透明度范围"""
        alpha_start = values.get('alpha_start', 0.0)
        if v <= alpha_start:
            raise ValueError("结束透明度必须大于起始透明度")
        return v


# 视频处理参数模型
class VideoResizeParams(BaseModel):
    """视频缩放参数"""
    width: Optional[int] = Field(None, ge=1, le=4096, description="目标宽度")
    height: Optional[int] = Field(None, ge=1, le=4096, description="目标高度")
    scale_mode: ScaleModeType = Field(default=ScaleModeType.FIT, description="缩放模式")
    scale_ratio: Optional[float] = Field(None, ge=0.1, le=3.0, description="缩放比例")
    
    @root_validator
    def validate_resize_params(cls, values):
        """验证缩放参数"""
        width = values.get('width')
        height = values.get('height')
        scale_mode = values.get('scale_mode')
        scale_ratio = values.get('scale_ratio')
        
        if scale_mode == ScaleModeType.RATIO:
            if not scale_ratio:
                raise ValueError("比例缩放模式需要指定scale_ratio")
        elif scale_mode == ScaleModeType.CUSTOM:
            if not width or not height:
                raise ValueError("自定义模式需要指定width和height")
        
        return values


class VideoGrayscaleParams(BaseModel):
    """视频灰度转换参数"""
    pass  # 无需额外参数


class VideoExtractFrameParams(BaseModel):
    """视频帧提取参数"""
    frame_number: int = Field(default=0, ge=0, le=999999, description="帧号")


class VideoEdgeDetectionParams(BaseModel):
    """视频边缘检测参数"""
    low_threshold: int = Field(default=50, ge=0, le=255, description="低阈值")
    high_threshold: int = Field(default=150, ge=0, le=255, description="高阈值")
    edge_type: EdgeType = Field(default=EdgeType.CANNY, description="边缘检测类型")
    
    @validator('high_threshold')
    def validate_thresholds(cls, v, values):
        """验证高阈值必须大于低阈值"""
        low_threshold = values.get('low_threshold', 0)
        if v <= low_threshold:
            raise ValueError("高阈值必须大于低阈值")
        return v


class VideoBlurParams(BaseModel):
    """视频模糊参数"""
    filter_type: FilterType = Field(default=FilterType.GAUSSIAN, description="滤镜类型")
    kernel_size: int = Field(default=5, ge=1, le=51, description="核大小")
    sigma: float = Field(default=1.0, ge=0.1, le=10.0, description="标准差")
    
    @validator('kernel_size')
    def validate_kernel_size(cls, v):
        """验证核大小必须为奇数"""
        if v % 2 == 0:
            raise ValueError("核大小必须为奇数")
        return v


class VideoBinaryParams(BaseModel):
    """视频二值化参数"""
    threshold: int = Field(default=127, ge=0, le=255, description="阈值")
    max_value: int = Field(default=255, ge=0, le=255, description="最大值")
    threshold_type: ThresholdType = Field(default=ThresholdType.BINARY, description="阈值类型")


class VideoTransformParams(BaseModel):
    """视频变换参数"""
    transform_type: TransformType = Field(default=TransformType.ROTATE_90, description="变换类型")
    angle: Optional[float] = Field(None, ge=-360.0, le=360.0, description="旋转角度")
    
    @root_validator
    def validate_transform_params(cls, values):
        """验证变换参数"""
        transform_type = values.get('transform_type')
        angle = values.get('angle')
        
        if transform_type == TransformType.ROTATE_CUSTOM:
            if angle is None:
                raise ValueError("自定义旋转需要指定angle参数")
        
        return values


class VideoThumbnailParams(BaseModel):
    """视频缩略图参数"""
    width: int = Field(default=256, ge=64, le=1024, description="缩略图宽度")
    height: int = Field(default=256, ge=64, le=1024, description="缩略图高度")
    frame_position: float = Field(default=0.1, ge=0.0, le=1.0, description="帧位置(0-1)")


# 参数类型映射
PARAMETER_MODELS = {
    TaskType.IMAGE_SHARPEN: ImageSharpenParams,
    TaskType.IMAGE_GRAYSCALE: ImageGrayscaleParams,
    TaskType.IMAGE_EDGE_DETECTION: ImageEdgeDetectionParams,
    TaskType.IMAGE_GAMMA_CORRECTION: ImageGammaParams,
    TaskType.IMAGE_FUSION: ImageFusionParams,
    TaskType.IMAGE_STITCHING: ImageStitchingParams,
    TaskType.BEAUTY_ENHANCEMENT: BeautyEnhancementParams,
    TaskType.TEXTURE_TRANSFER: TextureTransferParams,
    
    TaskType.VIDEO_RESIZE: VideoResizeParams,
    TaskType.VIDEO_GRAYSCALE: VideoGrayscaleParams,
    TaskType.VIDEO_EXTRACT_FRAME: VideoExtractFrameParams,
    TaskType.VIDEO_EDGE_DETECTION: VideoEdgeDetectionParams,
    TaskType.VIDEO_BLUR: VideoBlurParams,
    TaskType.VIDEO_BINARY: VideoBinaryParams,
    TaskType.VIDEO_TRANSFORM: VideoTransformParams,
    TaskType.VIDEO_THUMBNAIL: VideoThumbnailParams,
}


def validate_task_parameters(task_type: TaskType, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证任务参数
    
    Args:
        task_type: 任务类型
        parameters: 参数字典
        
    Returns:
        验证后的参数字典
        
    Raises:
        ValueError: 参数验证失败
    """
    param_model = PARAMETER_MODELS.get(task_type)
    if not param_model:
        raise ValueError(f"不支持的任务类型: {task_type}")
    
    try:
        # 使用Pydantic模型验证参数
        validated_params = param_model(**parameters)
        return validated_params.dict()
    except Exception as e:
        raise ValueError(f"参数验证失败: {str(e)}")
