"""
参数验证服务
"""
from typing import Dict, Any, List, Tuple, Optional
import structlog

from app.schemas.task_submit import TaskType, FileInput
from app.schemas.task_parameters import validate_task_parameters, PARAMETER_MODELS

logger = structlog.get_logger()


class ParameterValidationError(Exception):
    """参数验证错误"""
    def __init__(self, message: str, field: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.field = field
        self.details = details or {}
        super().__init__(message)


class ParameterValidator:
    """参数验证器"""
    
    @classmethod
    def validate_task_submission(
        cls,
        task_type: TaskType,
        files: List[FileInput],
        parameters: Dict[str, Any]
    ) -> Tuple[bool, Optional[str], Dict[str, Any]]:
        """
        验证完整的任务提交
        
        Args:
            task_type: 任务类型
            files: 文件列表
            parameters: 参数字典
            
        Returns:
            (是否有效, 错误信息, 验证后的参数)
        """
        try:
            # 1. 验证文件与任务类型的兼容性
            cls._validate_files_compatibility(task_type, files)
            
            # 2. 验证参数
            validated_params = validate_task_parameters(task_type, parameters)
            
            # 3. 验证文件与参数的一致性
            cls._validate_files_parameters_consistency(task_type, files, validated_params)
            
            logger.info(
                "parameter_validation_success",
                task_type=task_type,
                file_count=len(files),
                param_count=len(validated_params)
            )
            
            return True, None, validated_params
            
        except ParameterValidationError as e:
            logger.warning(
                "parameter_validation_error",
                task_type=task_type,
                error=e.message,
                field=e.field,
                details=e.details
            )
            return False, e.message, {}
        except Exception as e:
            logger.error(
                "parameter_validation_system_error",
                task_type=task_type,
                error=str(e)
            )
            return False, f"参数验证系统错误: {str(e)}", {}
    
    @classmethod
    def _validate_files_compatibility(cls, task_type: TaskType, files: List[FileInput]):
        """验证文件与任务类型的兼容性"""
        
        # 检查文件类型
        image_extensions = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp'}
        video_extensions = {'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv'}
        
        image_tasks = {
            TaskType.IMAGE_SHARPEN, TaskType.IMAGE_GRAYSCALE, TaskType.IMAGE_EDGE_DETECTION,
            TaskType.IMAGE_GAMMA_CORRECTION, TaskType.IMAGE_FUSION, TaskType.IMAGE_STITCHING,
            TaskType.BEAUTY_ENHANCEMENT, TaskType.TEXTURE_TRANSFER
        }
        
        video_tasks = {
            TaskType.VIDEO_RESIZE, TaskType.VIDEO_GRAYSCALE, TaskType.VIDEO_EXTRACT_FRAME,
            TaskType.VIDEO_EDGE_DETECTION, TaskType.VIDEO_BLUR, TaskType.VIDEO_BINARY,
            TaskType.VIDEO_TRANSFORM, TaskType.VIDEO_THUMBNAIL
        }
        
        for file in files:
            file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
            
            if task_type in image_tasks and file_ext not in image_extensions:
                raise ParameterValidationError(
                    f"图像处理任务不支持文件类型: {file_ext}",
                    field="files",
                    details={"filename": file.filename, "extension": file_ext}
                )
            
            if task_type in video_tasks and file_ext not in video_extensions:
                raise ParameterValidationError(
                    f"视频处理任务不支持文件类型: {file_ext}",
                    field="files", 
                    details={"filename": file.filename, "extension": file_ext}
                )
    
    @classmethod
    def _validate_files_parameters_consistency(
        cls, 
        task_type: TaskType, 
        files: List[FileInput], 
        parameters: Dict[str, Any]
    ):
        """验证文件与参数的一致性"""
        
        # 视频帧提取任务的特殊验证
        if task_type == TaskType.VIDEO_EXTRACT_FRAME:
            frame_number = parameters.get('frame_number', 0)
            # 这里可以添加更复杂的验证，比如检查视频长度
            # 暂时只做基础验证
            if frame_number < 0:
                raise ParameterValidationError(
                    "帧号不能为负数",
                    field="frame_number",
                    details={"frame_number": frame_number}
                )
        
        # 视频缩放任务的特殊验证
        if task_type == TaskType.VIDEO_RESIZE:
            scale_mode = parameters.get('scale_mode', 'fit')
            width = parameters.get('width')
            height = parameters.get('height')
            scale_ratio = parameters.get('scale_ratio')
            
            # 检查文件大小与目标尺寸的合理性
            for file in files:
                if file.size > 2 * 1024 * 1024 * 1024:  # 2GB
                    if width and height and (width > 1920 or height > 1080):
                        raise ParameterValidationError(
                            "大文件不建议缩放到过高分辨率",
                            field="resolution",
                            details={
                                "file_size": file.size,
                                "target_width": width,
                                "target_height": height
                            }
                        )
        
        # 图像拼接任务的特殊验证
        if task_type == TaskType.IMAGE_STITCHING:
            if len(files) > 20:
                raise ParameterValidationError(
                    "图像拼接任务最多支持20个文件",
                    field="files",
                    details={"file_count": len(files)}
                )
    
    @classmethod
    def get_parameter_schema(cls, task_type: TaskType) -> Dict[str, Any]:
        """
        获取任务类型的参数模式
        
        Args:
            task_type: 任务类型
            
        Returns:
            参数模式字典
        """
        param_model = PARAMETER_MODELS.get(task_type)
        if not param_model:
            return {}
        
        return param_model.schema()
    
    @classmethod
    def get_default_parameters(cls, task_type: TaskType) -> Dict[str, Any]:
        """
        获取任务类型的默认参数
        
        Args:
            task_type: 任务类型
            
        Returns:
            默认参数字典
        """
        param_model = PARAMETER_MODELS.get(task_type)
        if not param_model:
            return {}
        
        try:
            # 创建默认实例并返回其字典表示
            default_instance = param_model()
            return default_instance.dict()
        except Exception as e:
            logger.warning(
                "get_default_parameters_error",
                task_type=task_type,
                error=str(e)
            )
            return {}
    
    @classmethod
    def validate_parameter_ranges(cls, task_type: TaskType, parameters: Dict[str, Any]) -> List[str]:
        """
        验证参数范围并返回警告信息
        
        Args:
            task_type: 任务类型
            parameters: 参数字典
            
        Returns:
            警告信息列表
        """
        warnings = []
        
        # 图像锐化强度警告
        if task_type == TaskType.IMAGE_SHARPEN:
            intensity = parameters.get('intensity', 1.0)
            if intensity > 3.0:
                warnings.append("锐化强度过高可能导致图像失真")
        
        # 伽马校正警告
        if task_type == TaskType.IMAGE_GAMMA_CORRECTION:
            gamma = parameters.get('gamma', 1.0)
            if gamma < 0.3 or gamma > 2.5:
                warnings.append("极端的伽马值可能导致图像过暗或过亮")
        
        # 视频缩放警告
        if task_type == TaskType.VIDEO_RESIZE:
            scale_ratio = parameters.get('scale_ratio')
            if scale_ratio and scale_ratio > 2.0:
                warnings.append("大幅放大可能导致视频质量下降")
        
        # 模糊核大小警告
        if task_type == TaskType.VIDEO_BLUR:
            kernel_size = parameters.get('kernel_size', 5)
            if kernel_size > 25:
                warnings.append("过大的模糊核可能导致处理时间显著增加")
        
        return warnings
    
    @classmethod
    def estimate_processing_complexity(cls, task_type: TaskType, files: List[FileInput], parameters: Dict[str, Any]) -> str:
        """
        估算处理复杂度
        
        Args:
            task_type: 任务类型
            files: 文件列表
            parameters: 参数字典
            
        Returns:
            复杂度等级: "low", "medium", "high", "very_high"
        """
        total_size = sum(f.size for f in files)
        file_count = len(files)
        
        # 基础复杂度
        base_complexity = {
            TaskType.IMAGE_GRAYSCALE: 1,
            TaskType.VIDEO_THUMBNAIL: 1,
            TaskType.VIDEO_EXTRACT_FRAME: 1,
            
            TaskType.IMAGE_SHARPEN: 2,
            TaskType.IMAGE_GAMMA_CORRECTION: 2,
            TaskType.VIDEO_GRAYSCALE: 2,
            TaskType.VIDEO_BINARY: 2,
            
            TaskType.IMAGE_EDGE_DETECTION: 3,
            TaskType.VIDEO_RESIZE: 3,
            TaskType.VIDEO_BLUR: 3,
            TaskType.VIDEO_TRANSFORM: 3,
            
            TaskType.IMAGE_FUSION: 4,
            TaskType.BEAUTY_ENHANCEMENT: 4,
            TaskType.VIDEO_EDGE_DETECTION: 4,
            
            TaskType.IMAGE_STITCHING: 5,
            TaskType.TEXTURE_TRANSFER: 5,
        }.get(task_type, 3)
        
        # 文件大小调整
        if total_size > 1024 * 1024 * 1024:  # 1GB
            base_complexity += 2
        elif total_size > 100 * 1024 * 1024:  # 100MB
            base_complexity += 1
        
        # 文件数量调整
        if file_count > 10:
            base_complexity += 1
        if file_count > 50:
            base_complexity += 1
        
        # 参数复杂度调整
        if task_type == TaskType.VIDEO_RESIZE:
            scale_ratio = parameters.get('scale_ratio', 1.0)
            if scale_ratio and scale_ratio > 1.5:
                base_complexity += 1
        
        # 返回复杂度等级
        if base_complexity <= 2:
            return "low"
        elif base_complexity <= 4:
            return "medium"
        elif base_complexity <= 6:
            return "high"
        else:
            return "very_high"
