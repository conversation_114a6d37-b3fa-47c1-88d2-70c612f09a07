"""
示例测试：展示如何使用测试fixtures和辅助工具
这个文件展示了如何有效地使用我们创建的测试工具
"""
import pytest
from unittest.mock import patch
from tests.test_helpers import (
    TestDataFactory, MockFactory, ResponseValidator, 
    PathValidator, AssertionHelpers
)


class TestExampleUsingHelpers:
    """示例：使用测试辅助工具的测试"""
    
    def test_storage_path_manager_with_helpers(self, mock_datetime, path_validator):
        """示例：使用辅助工具测试StoragePathManager"""
        from app.utils.storage_path import StoragePathManager, StorageType
        
        # 使用TestDataFactory创建测试数据
        file_data = TestDataFactory.create_file_data(
            filename="example.jpg",
            content_type="image/jpeg"
        )
        
        # 生成路径
        object_name, file_id = StoragePathManager.generate_file_path(
            filename=file_data["filename"],
            storage_type=StorageType.UPLOAD
        )
        
        # 使用AssertionHelpers进行断言
        AssertionHelpers.assert_valid_uuid(file_id)
        AssertionHelpers.assert_valid_storage_path(
            object_name, 
            storage_type="uploads", 
            category="images"
        )
        
        # 使用PathValidator进行验证
        assert path_validator["storage_path"](object_name, "uploads", "images")
        assert path_validator["uuid"](file_id)
    
    def test_minio_service_with_mock_factory(self):
        """示例：使用MockFactory测试MinIOService"""
        # 使用MockFactory创建Mock对象
        mock_service = MockFactory.create_minio_service_mock(
            initialize_return=True,
            presigned_url="https://example.com/presigned",
            upload_success=True
        )
        
        # 使用TestDataFactory创建测试文件
        file_data = TestDataFactory.create_image_file(width=100, height=100)
        
        # 测试上传
        result = mock_service.upload_file(
            file_data=file_data["file_obj"],
            object_name="uploads/images/test.bmp",
            content_type=file_data["content_type"]
        )
        
        assert result is True
        mock_service.upload_file.assert_called_once()
    
    def test_api_response_validation(self):
        """示例：使用ResponseValidator验证API响应"""
        # 模拟API响应
        api_response = {
            "file_id": "test-file-id-123",
            "object_name": "uploads/images/test.jpg",
            "presigned_url": "https://example.com/presigned-url",
            "expires_in": 3600,
            "method": "PUT",
            "headers": {"Content-Type": "image/jpeg"}
        }
        
        # 使用ResponseValidator验证响应格式
        assert ResponseValidator.validate_presigned_url_response(api_response)
        
        # 使用AssertionHelpers验证响应结构
        required_fields = ["file_id", "object_name", "presigned_url", "expires_in", "method"]
        AssertionHelpers.assert_response_structure(api_response, required_fields)
    
    def test_file_operations_with_test_data(self):
        """示例：使用TestDataFactory进行文件操作测试"""
        # 创建不同类型的测试文件
        image_file = TestDataFactory.create_image_file(width=200, height=150)
        video_file = TestDataFactory.create_video_file()
        large_file = TestDataFactory.create_large_file(size_mb=1)
        
        # 验证文件数据
        assert image_file["content_type"] == "image/bmp"
        assert image_file["filename"].endswith(".bmp")
        assert len(image_file["content"]) > 1000  # BMP文件应该有一定大小
        
        assert video_file["content_type"] == "video/mp4"
        assert video_file["filename"].endswith(".mp4")
        
        assert large_file["size"] == 1024 * 1024  # 1MB
        assert large_file["content_type"] == "application/octet-stream"
        
        # 使用AssertionHelpers验证文件内容
        AssertionHelpers.assert_file_content_equal(
            image_file["file_obj"], 
            image_file["content"]
        )
    
    @patch('app.api.files.settings')
    def test_api_with_mock_settings(self, mock_settings_patch):
        """示例：使用MockFactory创建settings mock"""
        # 使用MockFactory创建settings mock
        mock_settings = MockFactory.create_settings_mock()
        mock_settings_patch.return_value = mock_settings
        
        # 测试文件类型验证逻辑
        allowed_extensions = mock_settings.get_allowed_image_extensions()
        
        assert 'jpg' in allowed_extensions
        assert 'png' in allowed_extensions
        assert 'gif' in allowed_extensions
        
        # 验证配置值
        assert mock_settings.MINIO_BUCKET_NAME == "test-bucket"
        assert mock_settings.MINIO_ENDPOINT == "localhost:9001"
    
    def test_error_handling_with_validators(self):
        """示例：使用验证器测试错误处理"""
        # 测试无效的UUID
        invalid_uuid = "not-a-uuid"
        assert not PathValidator.validate_uuid(invalid_uuid)
        
        # 测试无效的存储路径
        invalid_path = "invalid-path"
        assert not PathValidator.validate_storage_path(invalid_path)
        
        # 测试错误响应格式
        error_response = {"detail": "File not found"}
        assert ResponseValidator.validate_error_response(error_response)
        
        invalid_error_response = {"message": "Error"}  # 错误的字段名
        assert not ResponseValidator.validate_error_response(invalid_error_response)
    
    def test_comprehensive_workflow_example(self, test_file_generator, test_uuid_generator):
        """示例：综合工作流测试"""
        # 1. 生成测试数据
        file_data = test_file_generator(
            filename="workflow_test.jpg",
            content=b"test image content",
            content_type="image/jpeg"
        )
        
        file_id = test_uuid_generator()
        
        # 2. 验证生成的数据
        AssertionHelpers.assert_valid_uuid(file_id)
        assert file_data["filename"] == "workflow_test.jpg"
        assert file_data["content_type"] == "image/jpeg"
        
        # 3. 模拟存储路径生成
        object_name = f"uploads/images/{file_id}/workflow_test.jpg"
        
        # 4. 验证路径格式
        AssertionHelpers.assert_valid_storage_path(
            object_name,
            storage_type="uploads",
            category="images"
        )
        
        # 5. 模拟API响应
        api_response = {
            "file_id": file_id,
            "object_name": object_name,
            "presigned_url": "https://example.com/presigned",
            "expires_in": 3600,
            "method": "PUT"
        }
        
        # 6. 验证响应格式
        assert ResponseValidator.validate_presigned_url_response(api_response)
        
        # 7. 验证文件内容
        AssertionHelpers.assert_file_content_equal(
            file_data["file_obj"],
            file_data["content"]
        )


class TestFixturesIntegration:
    """测试fixtures集成"""
    
    def test_mock_minio_service_fixture(self, mock_minio_service):
        """测试mock_minio_service fixture"""
        # 验证fixture提供的mock对象
        assert mock_minio_service.bucket_name == "test-bucket"
        assert mock_minio_service._initialized is True
        
        # 测试方法调用
        result = mock_minio_service.file_exists("test.txt")
        assert result is not None  # Mock应该返回一些值
    
    def test_sample_file_data_fixture(self, sample_file_data):
        """测试sample_file_data fixture"""
        assert sample_file_data["filename"] == "test.jpg"
        assert sample_file_data["content"] == b"fake image content"
        assert sample_file_data["content_type"] == "image/jpeg"
        assert sample_file_data["size"] == 18
    
    def test_fixed_datetime_fixture(self, fixed_datetime):
        """测试fixed_datetime fixture"""
        assert fixed_datetime.year == 2025
        assert fixed_datetime.month == 8
        assert fixed_datetime.day == 5
        assert fixed_datetime.hour == 12
        assert fixed_datetime.minute == 30
        assert fixed_datetime.second == 45
    
    def test_path_validator_fixture(self, path_validator):
        """测试path_validator fixture"""
        # 测试UUID验证
        valid_uuid = "550e8400-e29b-41d4-a716-************"
        assert path_validator["uuid"](valid_uuid) is True
        
        # 测试路径验证
        valid_path = f"uploads/images/{valid_uuid}/test.jpg"
        assert path_validator["storage_path"](valid_path) is True
        
        # 测试datetime验证
        valid_datetime = "2025-08-05T12:30:45"
        assert path_validator["datetime_iso"](valid_datetime) is True
