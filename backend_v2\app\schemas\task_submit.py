"""
任务提交相关的Pydantic模式
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
import enum

from app.models.task import TaskStatus


class TaskType(str, enum.Enum):
    """任务类型枚举"""
    # 图像处理任务
    IMAGE_SHARPEN = "image_sharpen"
    IMAGE_GRAYSCALE = "image_grayscale"
    IMAGE_EDGE_DETECTION = "image_edge_detection"
    IMAGE_GAMMA_CORRECTION = "image_gamma_correction"
    IMAGE_FUSION = "image_fusion"
    IMAGE_STITCHING = "image_stitching"
    BEAUTY_ENHANCEMENT = "beauty_enhancement"
    TEXTURE_TRANSFER = "texture_transfer"
    
    # 视频处理任务
    VIDEO_RESIZE = "video_resize"
    VIDEO_GRAYSCALE = "video_grayscale"
    VIDEO_EXTRACT_FRAME = "video_extract_frame"
    VIDEO_EDGE_DETECTION = "video_edge_detection"
    VIDEO_BLUR = "video_blur"
    VIDEO_BINARY = "video_binary"
    VIDEO_TRANSFORM = "video_transform"
    VIDEO_THUMBNAIL = "video_thumbnail"


class QueueType(str, enum.Enum):
    """队列类型枚举"""
    CPU_POOL = "cpu"
    GPU_POOL = "gpu"
    IO_POOL = "io"
    HYBRID_POOL = "hybrid"


class FileInput(BaseModel):
    """文件输入模型"""
    file_id: str = Field(..., description="文件ID")
    filename: str = Field(..., description="原始文件名", max_length=255)
    size: int = Field(..., description="文件大小(字节)", ge=0)
    minio_path: str = Field(..., description="MinIO存储路径")
    content_type: Optional[str] = Field(None, description="文件MIME类型")
    
    @validator('filename')
    def validate_filename(cls, v):
        """验证文件名"""
        if not v or v.strip() == "":
            raise ValueError("文件名不能为空")
        # 检查文件扩展名
        allowed_extensions = {
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp',  # 图像
            'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv'  # 视频
        }
        ext = v.lower().split('.')[-1] if '.' in v else ''
        if ext not in allowed_extensions:
            raise ValueError(f"不支持的文件类型: {ext}")
        return v


class TaskSubmitRequest(BaseModel):
    """任务提交请求模型"""
    name: str = Field(..., description="任务名称", max_length=255)
    description: Optional[str] = Field(None, description="任务描述", max_length=1000)
    task_type: TaskType = Field(..., description="任务类型")
    files: List[FileInput] = Field(..., description="输入文件列表", min_items=1, max_items=100)
    parameters: Dict[str, Any] = Field(default_factory=dict, description="处理参数")
    priority: int = Field(default=0, description="任务优先级(0-10)", ge=0, le=10)
    
    @validator('files')
    def validate_files(cls, v, values):
        """根据任务类型验证文件数量"""
        task_type = values.get('task_type')
        if not task_type:
            return v
            
        # 双文件操作
        dual_file_operations = {TaskType.IMAGE_FUSION, TaskType.TEXTURE_TRANSFER}
        if task_type in dual_file_operations:
            if len(v) % 2 != 0:
                raise ValueError(f"操作 {task_type} 需要成对的文件")
        
        # 多文件操作
        multi_file_operations = {TaskType.IMAGE_STITCHING}
        if task_type in multi_file_operations:
            if len(v) < 2:
                raise ValueError(f"操作 {task_type} 至少需要2个文件")
        
        return v
    
    @validator('parameters')
    def validate_parameters(cls, v, values):
        """验证处理参数"""
        task_type = values.get('task_type')
        if not task_type:
            return v

        # 导入参数验证函数
        from app.schemas.task_parameters import validate_task_parameters

        try:
            # 使用专门的参数验证函数
            validated_params = validate_task_parameters(task_type, v)
            return validated_params
        except ValueError as e:
            raise ValueError(f"任务参数验证失败: {str(e)}")
        except Exception as e:
            raise ValueError(f"参数验证过程中发生错误: {str(e)}")


class BatchInfo(BaseModel):
    """批次信息模型"""
    batch_id: int = Field(..., description="批次ID")
    queue_type: QueueType = Field(..., description="分配的队列类型")
    file_count: int = Field(..., description="文件数量")
    estimated_duration: Optional[int] = Field(None, description="预估耗时(秒)")


class TaskSubmitResponse(BaseModel):
    """任务提交响应模型"""
    task_id: int = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    name: str = Field(..., description="任务名称")
    task_type: TaskType = Field(..., description="任务类型")
    created_at: datetime = Field(..., description="创建时间")
    
    # 批次信息
    batch_count: int = Field(..., description="批次数量")
    batches: List[BatchInfo] = Field(..., description="批次详情列表")
    
    # 队列信息
    total_files: int = Field(..., description="总文件数")
    estimated_total_duration: Optional[int] = Field(None, description="总预估耗时(秒)")
    
    # Celery任务信息
    celery_job_ids: List[str] = Field(..., description="Celery任务ID列表")
    
    class Config:
        from_attributes = True


class TaskSubmitError(BaseModel):
    """任务提交错误响应模型"""
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误信息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    invalid_files: Optional[List[str]] = Field(None, description="无效文件列表")
    invalid_parameters: Optional[List[str]] = Field(None, description="无效参数列表")


# 常用的错误响应
COMMON_ERRORS = {
    "INVALID_TASK_TYPE": TaskSubmitError(
        error_code="INVALID_TASK_TYPE",
        error_message="不支持的任务类型"
    ),
    "INVALID_FILE_COUNT": TaskSubmitError(
        error_code="INVALID_FILE_COUNT", 
        error_message="文件数量不符合要求"
    ),
    "INVALID_PARAMETERS": TaskSubmitError(
        error_code="INVALID_PARAMETERS",
        error_message="处理参数无效"
    ),
    "FILE_NOT_FOUND": TaskSubmitError(
        error_code="FILE_NOT_FOUND",
        error_message="指定的文件不存在"
    ),
    "QUEUE_FULL": TaskSubmitError(
        error_code="QUEUE_FULL",
        error_message="任务队列已满，请稍后重试"
    )
}
