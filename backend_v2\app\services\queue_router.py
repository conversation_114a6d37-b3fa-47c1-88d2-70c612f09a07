"""
队列路由服务
"""
from typing import List, Dict, Any
import structlog

from app.schemas.task_submit import TaskType, QueueType, FileInput

logger = structlog.get_logger()


class QueueRouter:
    """队列路由器"""
    
    # 基础路由规则：任务类型 -> 队列类型
    ROUTING_RULES = {
        # CPU密集型图像处理任务
        TaskType.IMAGE_SHARPEN: QueueType.CPU_POOL,
        TaskType.IMAGE_GRAYSCALE: QueueType.CPU_POOL,
        TaskType.IMAGE_EDGE_DETECTION: QueueType.CPU_POOL,
        TaskType.IMAGE_GAMMA_CORRECTION: QueueType.CPU_POOL,
        
        # GPU加速图像处理任务
        TaskType.BEAUTY_ENHANCEMENT: QueueType.GPU_POOL,
        TaskType.TEXTURE_TRANSFER: QueueType.GPU_POOL,
        TaskType.IMAGE_FUSION: QueueType.GPU_POOL,
        
        # 多文件处理任务
        TaskType.IMAGE_STITCHING: QueueType.CPU_POOL,
        
        # 视频处理任务 - 根据复杂度分配
        TaskType.VIDEO_RESIZE: QueueType.GPU_POOL,
        TaskType.VIDEO_GRAYSCALE: QueueType.CPU_POOL,
        TaskType.VIDEO_EXTRACT_FRAME: QueueType.IO_POOL,
        TaskType.VIDEO_THUMBNAIL: QueueType.IO_POOL,
        
        # 复杂视频处理 - 需要OpenCV+FFmpeg组合
        TaskType.VIDEO_EDGE_DETECTION: QueueType.HYBRID_POOL,
        TaskType.VIDEO_BLUR: QueueType.HYBRID_POOL,
        TaskType.VIDEO_BINARY: QueueType.HYBRID_POOL,
        TaskType.VIDEO_TRANSFORM: QueueType.HYBRID_POOL,
    }
    
    # 文件大小阈值 (字节)
    LARGE_FILE_THRESHOLD = 512 * 1024 * 1024  # 512MB
    HUGE_FILE_THRESHOLD = 2 * 1024 * 1024 * 1024  # 2GB
    
    @classmethod
    def get_queue_for_task(
        cls, 
        task_type: TaskType, 
        files: List[FileInput]
    ) -> QueueType:
        """
        根据任务类型和文件特征决定队列
        
        Args:
            task_type: 任务类型
            files: 文件列表
            
        Returns:
            推荐的队列类型
        """
        # 获取基础队列类型
        base_queue = cls.ROUTING_RULES.get(task_type, QueueType.CPU_POOL)
        
        # 计算文件统计信息
        total_size = sum(f.size for f in files)
        max_file_size = max(f.size for f in files) if files else 0
        file_count = len(files)
        
        logger.info(
            "queue_routing_analysis",
            task_type=task_type,
            base_queue=base_queue,
            file_count=file_count,
            total_size=total_size,
            max_file_size=max_file_size
        )
        
        # 大文件处理策略
        if max_file_size > cls.HUGE_FILE_THRESHOLD:
            # 超大文件优先使用IO池或混合池
            if base_queue == QueueType.CPU_POOL:
                return QueueType.IO_POOL
            elif base_queue == QueueType.GPU_POOL:
                return QueueType.HYBRID_POOL
        elif max_file_size > cls.LARGE_FILE_THRESHOLD:
            # 大文件考虑IO优化
            if base_queue == QueueType.CPU_POOL and cls._is_io_intensive(task_type):
                return QueueType.IO_POOL
        
        # 批量文件处理策略
        if file_count > 10:
            # 大批量文件优先使用CPU池的并行处理能力
            if base_queue == QueueType.GPU_POOL:
                # GPU池通常单进程处理，大批量时可能不如CPU池效率高
                return QueueType.CPU_POOL
        
        return base_queue
    
    @classmethod
    def _is_io_intensive(cls, task_type: TaskType) -> bool:
        """判断任务是否为IO密集型"""
        io_intensive_tasks = {
            TaskType.VIDEO_EXTRACT_FRAME,
            TaskType.VIDEO_THUMBNAIL,
            TaskType.IMAGE_STITCHING,  # 多文件读取
        }
        return task_type in io_intensive_tasks
    
    @classmethod
    def get_batch_strategy(
        cls, 
        task_type: TaskType, 
        files: List[FileInput]
    ) -> Dict[str, Any]:
        """
        获取批次分割策略
        
        Args:
            task_type: 任务类型
            files: 文件列表
            
        Returns:
            批次策略配置
        """
        queue_type = cls.get_queue_for_task(task_type, files)
        file_count = len(files)
        total_size = sum(f.size for f in files)
        
        # 根据队列类型和文件特征决定批次大小
        if queue_type == QueueType.CPU_POOL:
            # CPU池支持高并发，可以较大批次
            batch_size = min(20, max(1, file_count // 4))
        elif queue_type == QueueType.GPU_POOL:
            # GPU池资源珍贵，较小批次避免显存不足
            batch_size = min(5, max(1, file_count // 8))
        elif queue_type == QueueType.IO_POOL:
            # IO池优化吞吐量，中等批次
            batch_size = min(10, max(1, file_count // 6))
        else:  # HYBRID_POOL
            # 混合池平衡各种资源，中等批次
            batch_size = min(8, max(1, file_count // 5))
        
        # 根据文件大小调整批次
        avg_file_size = total_size / file_count if file_count > 0 else 0
        if avg_file_size > cls.LARGE_FILE_THRESHOLD:
            # 大文件减少批次大小
            batch_size = max(1, batch_size // 2)
        
        return {
            "queue_type": queue_type,
            "batch_size": batch_size,
            "estimated_batches": (file_count + batch_size - 1) // batch_size,
            "priority_boost": cls._get_priority_boost(task_type, total_size)
        }
    
    @classmethod
    def _get_priority_boost(cls, task_type: TaskType, total_size: int) -> int:
        """
        根据任务类型和大小计算优先级提升
        
        Args:
            task_type: 任务类型
            total_size: 总文件大小
            
        Returns:
            优先级提升值 (0-3)
        """
        boost = 0
        
        # 快速任务优先级提升
        quick_tasks = {
            TaskType.IMAGE_GRAYSCALE,
            TaskType.VIDEO_THUMBNAIL,
            TaskType.VIDEO_EXTRACT_FRAME
        }
        if task_type in quick_tasks:
            boost += 1
        
        # 小文件优先级提升
        if total_size < 10 * 1024 * 1024:  # 10MB以下
            boost += 1
        
        # 复杂任务优先级降低
        complex_tasks = {
            TaskType.IMAGE_STITCHING,
            TaskType.TEXTURE_TRANSFER,
            TaskType.VIDEO_EDGE_DETECTION
        }
        if task_type in complex_tasks:
            boost -= 1
        
        return max(0, min(3, boost))
    
    @classmethod
    def estimate_duration(
        cls, 
        task_type: TaskType, 
        files: List[FileInput]
    ) -> int:
        """
        估算任务执行时间
        
        Args:
            task_type: 任务类型
            files: 文件列表
            
        Returns:
            预估执行时间(秒)
        """
        # 基础处理时间 (秒/MB)
        base_times = {
            # 图像处理 - 相对较快
            TaskType.IMAGE_GRAYSCALE: 0.1,
            TaskType.IMAGE_SHARPEN: 0.2,
            TaskType.IMAGE_EDGE_DETECTION: 0.3,
            TaskType.IMAGE_GAMMA_CORRECTION: 0.15,
            TaskType.IMAGE_FUSION: 0.5,
            TaskType.IMAGE_STITCHING: 1.0,
            TaskType.BEAUTY_ENHANCEMENT: 0.8,
            TaskType.TEXTURE_TRANSFER: 1.2,
            
            # 视频处理 - 相对较慢
            TaskType.VIDEO_GRAYSCALE: 0.5,
            TaskType.VIDEO_RESIZE: 1.0,
            TaskType.VIDEO_EXTRACT_FRAME: 0.1,
            TaskType.VIDEO_THUMBNAIL: 0.2,
            TaskType.VIDEO_EDGE_DETECTION: 2.0,
            TaskType.VIDEO_BLUR: 1.5,
            TaskType.VIDEO_BINARY: 0.8,
            TaskType.VIDEO_TRANSFORM: 1.2,
        }
        
        base_time = base_times.get(task_type, 0.5)
        total_size_mb = sum(f.size for f in files) / (1024 * 1024)
        
        # 基础时间计算
        estimated_time = int(total_size_mb * base_time)
        
        # 队列类型调整
        queue_type = cls.get_queue_for_task(task_type, files)
        if queue_type == QueueType.GPU_POOL:
            estimated_time = int(estimated_time * 0.6)  # GPU加速
        elif queue_type == QueueType.IO_POOL:
            estimated_time = int(estimated_time * 1.2)  # IO瓶颈
        elif queue_type == QueueType.HYBRID_POOL:
            estimated_time = int(estimated_time * 0.8)  # 混合优化
        
        # 最小时间保证
        return max(5, estimated_time)
