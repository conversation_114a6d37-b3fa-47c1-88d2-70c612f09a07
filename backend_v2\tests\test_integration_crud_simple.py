"""
简化的CRUD操作集成测试
测试基本的CRUD功能，验证数据库操作正确性
"""
import pytest
from sqlalchemy import create_engine, Column, Integer, String, BigInteger, Enum as SQLEnum, DateTime
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
from pydantic import BaseModel
from typing import Optional

from app.models.file import FileType, FileStatus


# 创建测试专用的Base和模型
TestBase = declarative_base()


class TestFile(TestBase):
    """测试专用的File模型"""
    __tablename__ = "files"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    file_type = Column(SQLEnum(FileType), nullable=False)
    content_type = Column(String(100))
    storage_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger)
    status = Column(SQLEnum(FileStatus), default=FileStatus.PENDING, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)


class TestFileCreate(BaseModel):
    """测试专用的文件创建schema"""
    filename: str
    file_type: FileType
    content_type: Optional[str] = None
    storage_path: str
    file_size: Optional[int] = None
    status: FileStatus = FileStatus.PENDING


class TestCRUDFile:
    """测试专用的文件CRUD操作类"""
    
    def __init__(self, model):
        self.model = model
    
    def get(self, db, id):
        return db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(self, db, *, skip: int = 0, limit: int = 100):
        return db.query(self.model).offset(skip).limit(limit).all()
    
    def create(self, db, *, obj_in):
        if hasattr(obj_in, 'model_dump'):
            obj_in_data = obj_in.model_dump()
        else:
            obj_in_data = obj_in.dict()
        
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(self, db, *, db_obj, obj_in):
        if hasattr(obj_in, 'model_dump'):
            update_data = obj_in.model_dump(exclude_unset=True)
        elif hasattr(obj_in, 'dict'):
            update_data = obj_in.dict(exclude_unset=True)
        else:
            update_data = obj_in
        
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def remove(self, db, *, id):
        obj = db.get(self.model, id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj


# 测试fixtures
@pytest.fixture(scope="module")
def test_db_engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        "sqlite:///:memory:",
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    TestBase.metadata.create_all(bind=engine)
    yield engine
    TestBase.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_session(test_db_engine):
    """创建测试会话"""
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_db_engine,
        expire_on_commit=False
    )
    
    session = SessionLocal()
    try:
        yield session
        # 清理测试数据
        session.query(TestFile).delete()
        session.commit()
    finally:
        session.close()


@pytest.fixture
def crud_file():
    """文件CRUD操作实例"""
    return TestCRUDFile(TestFile)


@pytest.mark.integration
class TestBasicCRUDOperations:
    """测试基础CRUD操作"""
    
    def test_create_and_get_file(self, test_session, crud_file):
        """测试创建和获取文件"""
        # 创建文件
        file_data = TestFileCreate(
            filename="test.jpg",
            file_type=FileType.IMAGE,
            content_type="image/jpeg",
            storage_path="uploads/test.jpg",
            file_size=1024,
            status=FileStatus.UPLOADED
        )
        
        created_file = crud_file.create(test_session, obj_in=file_data)
        
        # 验证创建结果
        assert created_file.id is not None
        assert created_file.filename == "test.jpg"
        assert created_file.file_type == FileType.IMAGE
        assert created_file.status == FileStatus.UPLOADED
        assert created_file.file_size == 1024
        assert created_file.created_at is not None
        
        # 通过ID获取文件
        retrieved_file = crud_file.get(test_session, id=created_file.id)
        assert retrieved_file is not None
        assert retrieved_file.id == created_file.id
        assert retrieved_file.filename == created_file.filename
    
    def test_update_operations(self, test_session, crud_file):
        """测试更新操作"""
        # 创建文件
        file_data = TestFileCreate(
            filename="test.jpg",
            file_type=FileType.IMAGE,
            storage_path="uploads/test.jpg",
            status=FileStatus.UPLOADED
        )
        created_file = crud_file.create(test_session, obj_in=file_data)
        
        # 更新文件
        update_data = {"status": FileStatus.PROCESSED, "file_size": 2048}
        updated_file = crud_file.update(
            test_session, 
            db_obj=created_file, 
            obj_in=update_data
        )
        
        # 验证更新结果
        assert updated_file.id == created_file.id
        assert updated_file.status == FileStatus.PROCESSED
        assert updated_file.file_size == 2048
        assert updated_file.filename == created_file.filename
    
    def test_delete_operations(self, test_session, crud_file):
        """测试删除操作"""
        # 创建文件
        file_data = TestFileCreate(
            filename="test.jpg",
            file_type=FileType.IMAGE,
            storage_path="uploads/test.jpg"
        )
        created_file = crud_file.create(test_session, obj_in=file_data)
        file_id = created_file.id
        
        # 删除文件
        deleted_file = crud_file.remove(test_session, id=file_id)
        
        # 验证删除结果
        assert deleted_file.id == file_id
        
        # 验证文件已被删除
        retrieved_file = crud_file.get(test_session, id=file_id)
        assert retrieved_file is None
    
    def test_get_multi_operations(self, test_session, crud_file):
        """测试批量获取操作"""
        # 创建多个文件
        for i in range(5):
            file_data = TestFileCreate(
                filename=f"test_{i}.jpg",
                file_type=FileType.IMAGE,
                storage_path=f"uploads/test_{i}.jpg"
            )
            crud_file.create(test_session, obj_in=file_data)
        
        # 获取所有文件
        all_files = crud_file.get_multi(test_session)
        assert len(all_files) == 5
        
        # 测试分页
        first_page = crud_file.get_multi(test_session, skip=0, limit=2)
        assert len(first_page) == 2
        
        second_page = crud_file.get_multi(test_session, skip=2, limit=2)
        assert len(second_page) == 2
        
        third_page = crud_file.get_multi(test_session, skip=4, limit=2)
        assert len(third_page) == 1
    
    def test_file_types_and_status(self, test_session, crud_file):
        """测试文件类型和状态"""
        # 创建不同类型和状态的文件
        files_data = [
            ("image.jpg", FileType.IMAGE, FileStatus.UPLOADED),
            ("video.mp4", FileType.VIDEO, FileStatus.PROCESSING),
            ("doc.pdf", FileType.DOCUMENT, FileStatus.PROCESSED),
        ]
        
        created_files = []
        for filename, file_type, status in files_data:
            file_data = TestFileCreate(
                filename=filename,
                file_type=file_type,
                storage_path=f"uploads/{filename}",
                status=status
            )
            created_file = crud_file.create(test_session, obj_in=file_data)
            created_files.append(created_file)
        
        # 验证创建的文件
        assert len(created_files) == 3
        
        # 验证文件类型和状态
        image_file = created_files[0]
        assert image_file.file_type == FileType.IMAGE
        assert image_file.status == FileStatus.UPLOADED
        
        video_file = created_files[1]
        assert video_file.file_type == FileType.VIDEO
        assert video_file.status == FileStatus.PROCESSING
        
        doc_file = created_files[2]
        assert doc_file.file_type == FileType.DOCUMENT
        assert doc_file.status == FileStatus.PROCESSED


@pytest.mark.integration
class TestCRUDErrorHandling:
    """测试CRUD错误处理"""
    
    def test_get_nonexistent_record(self, test_session, crud_file):
        """测试获取不存在的记录"""
        nonexistent_file = crud_file.get(test_session, id=99999)
        assert nonexistent_file is None
    
    def test_delete_nonexistent_record(self, test_session, crud_file):
        """测试删除不存在的记录"""
        deleted_file = crud_file.remove(test_session, id=99999)
        assert deleted_file is None
