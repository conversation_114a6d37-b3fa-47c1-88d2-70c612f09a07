"""
数据库连接和会话管理简化集成测试
使用SQLite内存数据库进行测试，避免外部依赖
"""
import pytest
import time
import threading
from unittest.mock import patch, Mock
from sqlalchemy import text, create_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.exc import OperationalError
from sqlalchemy.pool import StaticPool

from app.core.database import Base


# 创建测试专用的数据库引擎和会话
@pytest.fixture(scope="module")
def test_db_engine():
    """创建测试数据库引擎"""
    # 使用内存SQLite数据库进行测试
    engine = create_engine(
        "sqlite:///:memory:",
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # 清理
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_session_factory(test_db_engine):
    """创建测试会话工厂"""
    return sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_db_engine,
        expire_on_commit=False
    )


@pytest.mark.integration
class TestDatabaseConnection:
    """测试数据库连接功能"""
    
    def test_engine_configuration(self, test_db_engine):
        """测试数据库引擎配置"""
        # 验证引擎配置
        assert test_db_engine is not None
        assert test_db_engine.url.database == ":memory:"
        assert "sqlite" in str(test_db_engine.url)
    
    def test_database_connection_basic(self, test_db_engine):
        """测试基本数据库连接"""
        # 测试连接是否可用
        with test_db_engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            assert row[0] == 1
    
    def test_session_creation_and_cleanup(self, test_session_factory, test_db_engine):
        """测试会话创建和清理"""
        # 测试会话工厂创建会话
        session = test_session_factory()
        
        try:
            # 验证会话可用
            assert isinstance(session, Session)
            assert session.bind is test_db_engine
            
            # 测试简单查询
            result = session.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            assert row[0] == 1
            
        finally:
            session.close()
    
    def test_session_isolation(self, test_session_factory):
        """测试会话隔离性"""
        session1 = test_session_factory()
        session2 = test_session_factory()
        
        try:
            # 验证是不同的会话对象
            assert session1 is not session2
            assert id(session1) != id(session2)
            
            # 两个会话都应该可用
            result1 = session1.execute(text("SELECT 1 as test_value"))
            result2 = session2.execute(text("SELECT 2 as test_value"))
            
            assert result1.fetchone()[0] == 1
            assert result2.fetchone()[0] == 2
            
        finally:
            session1.close()
            session2.close()
    
    def test_transaction_basic(self, test_session_factory):
        """测试基本事务功能"""
        session = test_session_factory()
        
        try:
            # 开始事务
            session.begin()
            
            # 执行一些操作（这里只是简单查询）
            result = session.execute(text("SELECT 1 as test_value"))
            assert result.fetchone()[0] == 1
            
            # 提交事务
            session.commit()
            
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def test_transaction_rollback(self, test_session_factory):
        """测试事务回滚"""
        session = test_session_factory()
        
        try:
            # 开始事务
            session.begin()
            
            # 执行一些操作
            result = session.execute(text("SELECT 1 as test_value"))
            assert result.fetchone()[0] == 1
            
            # 手动回滚
            session.rollback()
            
            # 验证会话仍然可用
            result = session.execute(text("SELECT 2 as test_value"))
            assert result.fetchone()[0] == 2
            
        finally:
            session.close()


@pytest.mark.integration
class TestDatabaseConnectionPool:
    """测试数据库连接池功能"""
    
    def test_connection_pool_basic(self, test_db_engine):
        """测试连接池基本功能"""
        connections = []
        
        try:
            # 创建多个连接
            for i in range(5):
                conn = test_db_engine.connect()
                connections.append(conn)
                
                # 验证连接可用
                result = conn.execute(text(f"SELECT {i+1} as test_value"))
                assert result.fetchone()[0] == i + 1
            
            # 验证连接池状态（SQLite StaticPool可能不支持checkedout()）
            pool = test_db_engine.pool
            # SQLite StaticPool的行为可能不同，只验证连接可用
            assert len(connections) == 5
            
        finally:
            # 清理连接
            for conn in connections:
                conn.close()
    
    def test_connection_pool_concurrent_access(self, test_session_factory):
        """测试连接池并发访问"""
        results = []
        errors = []
        
        def worker(worker_id):
            """工作线程函数"""
            try:
                session = test_session_factory()
                try:
                    # 模拟一些数据库操作
                    result = session.execute(text(f"SELECT {worker_id} as worker_id"))
                    value = result.fetchone()[0]
                    results.append(value)
                    
                    # 模拟一些处理时间
                    time.sleep(0.01)
                    
                finally:
                    session.close()
            except Exception as e:
                errors.append(str(e))
        
        # 创建多个线程并发访问数据库
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0, f"Concurrent access errors: {errors}"
        assert len(results) == 10
        assert sorted(results) == list(range(10))


@pytest.mark.integration
class TestDatabaseErrorHandling:
    """测试数据库错误处理"""
    
    def test_session_error_handling(self, test_session_factory):
        """测试会话错误处理"""
        session = test_session_factory()
        
        try:
            # 执行一个无效的SQL语句
            with pytest.raises(OperationalError):
                session.execute(text("SELECT * FROM non_existent_table"))
                
        finally:
            session.close()
    
    def test_connection_recovery(self, test_db_engine):
        """测试连接恢复"""
        # 获取一个连接
        conn1 = test_db_engine.connect()
        
        try:
            # 正常查询
            result = conn1.execute(text("SELECT 1 as test_value"))
            assert result.fetchone()[0] == 1
            
        finally:
            conn1.close()
        
        # 创建新连接，应该能够正常工作
        conn2 = test_db_engine.connect()
        try:
            result = conn2.execute(text("SELECT 2 as test_value"))
            assert result.fetchone()[0] == 2
        finally:
            conn2.close()
    
    def test_mock_get_db_error_handling(self):
        """测试模拟get_db的错误处理"""
        from app.core.database import get_db

        # Mock一个会在yield时抛出异常的SessionLocal
        with patch('app.core.database.SessionLocal') as mock_session_local:
            mock_session = Mock()
            # 让SessionLocal()本身抛出异常，这样会在get_db的except块中被捕获
            mock_session_local.side_effect = Exception("Database connection error")

            # 测试get_db在初始化时的异常处理
            with pytest.raises(Exception, match="Database connection error"):
                db_generator = get_db()
                next(db_generator)

    def test_get_db_session_error_in_usage(self):
        """测试get_db会话使用中的错误处理"""
        from app.core.database import get_db

        # 创建一个正常的会话，但在使用时模拟异常
        db_generator = get_db()

        try:
            db = next(db_generator)

            # 验证会话可用
            assert db is not None

            # 模拟在会话使用过程中发生异常
            # 这里我们不直接测试rollback，因为异常是在yield之后发生的
            # 实际的rollback会在FastAPI的异常处理中触发

        except StopIteration:
            pytest.fail("get_db generator should yield a session")
        finally:
            # 清理生成器
            try:
                next(db_generator)
            except StopIteration:
                pass
