"""
文件CRUD操作集成测试
只测试File模型的CRUD操作，避免其他模型的SQLite兼容性问题
"""
import pytest
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, BigInteger, Enum as SQLEnum, DateTime
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import enum

from app.models.file import FileType, FileStatus
from app.crud.crud_file import CRUDFile
from app.schemas.file import FileCreate, FileUpdate
from pydantic import BaseModel
from typing import Optional


# 创建测试专用的Base和File模型
TestBase = declarative_base()


class TestFile(TestBase):
    """测试专用的File模型，避免复杂的关联关系"""
    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)

    # 关联信息
    batch_id = Column(Integer, comment="所属批次ID")

    # 基础信息
    filename = Column(String(255), nullable=False, comment="文件名")
    original_filename = Column(String(255), comment="原始文件名")
    file_type = Column(SQLEnum(FileType), nullable=False, comment="文件类型")
    content_type = Column(String(100), comment="MIME类型")

    # 存储信息
    storage_path = Column(String(500), nullable=False, comment="存储路径")
    storage_bucket = Column(String(100), comment="存储桶名称")

    # 文件信息
    file_size = Column(BigInteger, comment="文件大小(字节)")
    status = Column(SQLEnum(FileStatus), default=FileStatus.PENDING, nullable=False, comment="文件状态")
    file_metadata = Column(String(1000), comment="文件元数据JSON字符串")  # 简化为字符串
    checksum = Column(String(64), comment="文件校验和")
    upload_session_id = Column(String(255), comment="上传会话ID")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")


# 创建测试专用的schema
class TestFileCreate(BaseModel):
    """测试专用的文件创建schema"""
    filename: str
    original_filename: Optional[str] = None
    file_type: FileType
    content_type: Optional[str] = None
    storage_path: str
    storage_bucket: Optional[str] = None
    file_size: Optional[int] = None
    status: FileStatus = FileStatus.PENDING
    file_metadata: Optional[str] = None
    batch_id: Optional[int] = None
    checksum: Optional[str] = None
    upload_session_id: Optional[str] = None


# 创建测试专用的CRUD类
class TestCRUDFile:
    """测试专用的文件CRUD操作类"""
    
    def __init__(self, model):
        self.model = model
    
    def get(self, db, id):
        return db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(self, db, *, skip: int = 0, limit: int = 100):
        return db.query(self.model).offset(skip).limit(limit).all()
    
    def create(self, db, *, obj_in):
        if hasattr(obj_in, 'model_dump'):
            obj_in_data = obj_in.model_dump()
        else:
            obj_in_data = obj_in.dict()

        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(self, db, *, db_obj, obj_in):
        if hasattr(obj_in, 'model_dump'):
            update_data = obj_in.model_dump(exclude_unset=True)
        elif hasattr(obj_in, 'dict'):
            update_data = obj_in.dict(exclude_unset=True)
        else:
            update_data = obj_in
        
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def remove(self, db, *, id):
        obj = db.query(self.model).get(id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj
    
    def get_by_filename(self, db, *, filename: str):
        return db.query(self.model).filter(self.model.filename == filename).first()
    
    def get_by_type(self, db, *, file_type: FileType, skip: int = 0, limit: int = 100):
        return db.query(self.model).filter(self.model.file_type == file_type).offset(skip).limit(limit).all()
    
    def get_by_status(self, db, *, status: FileStatus, skip: int = 0, limit: int = 100):
        return db.query(self.model).filter(self.model.status == status).offset(skip).limit(limit).all()


# 测试fixtures
@pytest.fixture(scope="module")
def test_db_engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        "sqlite:///:memory:",
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    # 创建表
    TestBase.metadata.create_all(bind=engine)
    
    yield engine
    
    # 清理
    TestBase.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_session(test_db_engine):
    """创建测试会话"""
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_db_engine,
        expire_on_commit=False
    )

    session = SessionLocal()
    try:
        yield session
        # 清理测试数据
        session.query(TestFile).delete()
        session.commit()
    finally:
        session.close()


@pytest.fixture
def crud_file():
    """文件CRUD操作实例"""
    return TestCRUDFile(TestFile)


@pytest.fixture
def sample_file_data():
    """示例文件数据"""
    return {
        "filename": "test_image.jpg",
        "original_filename": "original_test.jpg",
        "file_type": FileType.IMAGE,
        "content_type": "image/jpeg",
        "storage_path": "uploads/images/2025/01/05/test_image.jpg",
        "storage_bucket": "clover-files",
        "file_size": 1024,
        "status": FileStatus.UPLOADED
    }


@pytest.mark.integration
class TestBasicCRUDOperations:
    """测试基础CRUD操作"""
    
    def test_create_and_get_file(self, test_session, crud_file, sample_file_data):
        """测试创建和获取文件"""
        # 创建文件
        file_create = TestFileCreate(**sample_file_data)
        created_file = crud_file.create(test_session, obj_in=file_create)
        
        # 验证创建结果
        assert created_file.id is not None
        assert created_file.filename == sample_file_data["filename"]
        assert created_file.file_type == FileType.IMAGE
        assert created_file.status == FileStatus.UPLOADED  # 从sample_file_data中设置
        assert created_file.file_size == 1024
        assert created_file.created_at is not None
        
        # 通过ID获取文件
        retrieved_file = crud_file.get(test_session, id=created_file.id)
        assert retrieved_file is not None
        assert retrieved_file.id == created_file.id
        assert retrieved_file.filename == created_file.filename
    
    def test_update_operations(self, test_session, crud_file, sample_file_data):
        """测试更新操作"""
        # 创建文件
        file_create = FileCreate(**sample_file_data)
        created_file = crud_file.create(test_session, obj_in=file_create)
        
        # 更新文件
        update_data = FileUpdate(
            status=FileStatus.PROCESSED,
            file_size=2048
        )
        updated_file = crud_file.update(
            test_session, 
            db_obj=created_file, 
            obj_in=update_data
        )
        
        # 验证更新结果
        assert updated_file.id == created_file.id
        assert updated_file.status == FileStatus.PROCESSED
        assert updated_file.file_size == 2048
        assert updated_file.filename == created_file.filename  # 未更新的字段保持不变
    
    def test_delete_operations(self, test_session, crud_file, sample_file_data):
        """测试删除操作"""
        # 创建文件
        file_create = FileCreate(**sample_file_data)
        created_file = crud_file.create(test_session, obj_in=file_create)
        file_id = created_file.id
        
        # 删除文件
        deleted_file = crud_file.remove(test_session, id=file_id)
        
        # 验证删除结果
        assert deleted_file.id == file_id
        
        # 验证文件已被删除
        retrieved_file = crud_file.get(test_session, id=file_id)
        assert retrieved_file is None
    
    def test_get_multi_operations(self, test_session, crud_file, sample_file_data):
        """测试批量获取操作"""
        # 创建多个文件
        files = []
        for i in range(5):
            file_data = sample_file_data.copy()
            file_data["filename"] = f"test_file_{i}.jpg"
            file_create = FileCreate(**file_data)
            created_file = crud_file.create(test_session, obj_in=file_create)
            files.append(created_file)
        
        # 获取所有文件
        all_files = crud_file.get_multi(test_session)
        assert len(all_files) == 5
        
        # 测试分页
        first_page = crud_file.get_multi(test_session, skip=0, limit=2)
        assert len(first_page) == 2
        
        second_page = crud_file.get_multi(test_session, skip=2, limit=2)
        assert len(second_page) == 2
        
        third_page = crud_file.get_multi(test_session, skip=4, limit=2)
        assert len(third_page) == 1


@pytest.mark.integration
class TestBusinessSpecificCRUD:
    """测试业务特定的CRUD操作"""
    
    def test_get_file_by_filename(self, test_session, crud_file, sample_file_data):
        """测试根据文件名获取文件"""
        # 创建文件
        file_create = FileCreate(**sample_file_data)
        created_file = crud_file.create(test_session, obj_in=file_create)
        
        # 根据文件名查找
        found_file = crud_file.get_by_filename(test_session, filename=sample_file_data["filename"])
        assert found_file is not None
        assert found_file.id == created_file.id
        assert found_file.filename == sample_file_data["filename"]
        
        # 查找不存在的文件
        not_found = crud_file.get_by_filename(test_session, filename="nonexistent.jpg")
        assert not_found is None
    
    def test_get_files_by_type(self, test_session, crud_file, sample_file_data):
        """测试根据文件类型获取文件"""
        # 创建不同类型的文件
        image_data = sample_file_data.copy()
        image_data["filename"] = "image.jpg"
        image_data["file_type"] = FileType.IMAGE
        image_create = FileCreate(**image_data)
        crud_file.create(test_session, obj_in=image_create)
        
        video_data = sample_file_data.copy()
        video_data["filename"] = "video.mp4"
        video_data["file_type"] = FileType.VIDEO
        video_data["content_type"] = "video/mp4"
        video_create = FileCreate(**video_data)
        crud_file.create(test_session, obj_in=video_create)
        
        # 获取图像文件
        image_files = crud_file.get_by_type(test_session, file_type=FileType.IMAGE)
        assert len(image_files) == 1
        assert image_files[0].file_type == FileType.IMAGE
        assert image_files[0].filename == "image.jpg"
        
        # 获取视频文件
        video_files = crud_file.get_by_type(test_session, file_type=FileType.VIDEO)
        assert len(video_files) == 1
        assert video_files[0].file_type == FileType.VIDEO
        assert video_files[0].filename == "video.mp4"
    
    def test_get_files_by_status(self, test_session, crud_file, sample_file_data):
        """测试根据文件状态获取文件"""
        # 创建不同状态的文件
        uploaded_data = sample_file_data.copy()
        uploaded_data["filename"] = "uploaded.jpg"
        uploaded_data["status"] = FileStatus.UPLOADED
        uploaded_create = FileCreate(**uploaded_data)
        crud_file.create(test_session, obj_in=uploaded_create)
        
        processed_data = sample_file_data.copy()
        processed_data["filename"] = "processed.jpg"
        processed_data["status"] = FileStatus.PROCESSED
        processed_create = FileCreate(**processed_data)
        crud_file.create(test_session, obj_in=processed_create)
        
        # 获取已上传的文件
        uploaded_files = crud_file.get_by_status(test_session, status=FileStatus.UPLOADED)
        assert len(uploaded_files) == 1
        assert uploaded_files[0].status == FileStatus.UPLOADED
        
        # 获取已处理的文件
        processed_files = crud_file.get_by_status(test_session, status=FileStatus.PROCESSED)
        assert len(processed_files) == 1
        assert processed_files[0].status == FileStatus.PROCESSED


@pytest.mark.integration
class TestCRUDErrorHandling:
    """测试CRUD错误处理"""
    
    def test_get_nonexistent_record(self, test_session, crud_file):
        """测试获取不存在的记录"""
        # 获取不存在的文件
        nonexistent_file = crud_file.get(test_session, id=99999)
        assert nonexistent_file is None
    
    def test_delete_nonexistent_record(self, test_session, crud_file):
        """测试删除不存在的记录"""
        # 尝试删除不存在的文件
        deleted_file = crud_file.remove(test_session, id=99999)
        assert deleted_file is None
