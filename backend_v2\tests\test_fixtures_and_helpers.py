"""
测试fixtures和辅助工具的功能
验证测试工具本身的正确性
"""
import pytest
import uuid
from io import Bytes<PERSON>
from unittest.mock import Mock

from tests.test_helpers import (
    TestDataFactory, MockFactory, ResponseValidator,
    PathValidator, AssertionHelpers
)


class TestTestDataFactory:
    """测试TestDataFactory"""
    
    def test_create_file_data_default(self):
        """测试创建默认文件数据"""
        file_data = TestDataFactory.create_file_data()
        
        assert "filename" in file_data
        assert "content" in file_data
        assert "content_type" in file_data
        assert "size" in file_data
        assert "file_obj" in file_data
        
        # 验证默认值
        assert file_data["filename"].startswith("test_")
        assert file_data["filename"].endswith(".txt")
        assert file_data["content_type"] == "text/plain"
        assert file_data["size"] == len(file_data["content"])
        assert isinstance(file_data["file_obj"], BytesIO)
    
    def test_create_file_data_custom(self):
        """测试创建自定义文件数据"""
        custom_content = b"custom content"
        file_data = TestDataFactory.create_file_data(
            filename="custom.jpg",
            content=custom_content,
            content_type="image/jpeg",
            size=len(custom_content)
        )
        
        assert file_data["filename"] == "custom.jpg"
        assert file_data["content"] == custom_content
        assert file_data["content_type"] == "image/jpeg"
        assert file_data["size"] == len(custom_content)
    
    def test_create_image_file(self):
        """测试创建图像文件"""
        image_data = TestDataFactory.create_image_file(width=50, height=50)
        
        assert image_data["filename"].endswith(".bmp")
        assert image_data["content_type"] == "image/bmp"
        assert len(image_data["content"]) > 100  # BMP文件应该有一定大小
        assert image_data["content"].startswith(b'BM')  # BMP文件签名
    
    def test_create_video_file(self):
        """测试创建视频文件"""
        video_data = TestDataFactory.create_video_file()
        
        assert video_data["filename"].endswith(".mp4")
        assert video_data["content_type"] == "video/mp4"
        assert len(video_data["content"]) > 100
        assert b'ftyp' in video_data["content"]  # MP4文件标识
    
    def test_create_large_file(self):
        """测试创建大文件"""
        large_data = TestDataFactory.create_large_file(size_mb=1)
        
        assert large_data["filename"].endswith(".bin")
        assert large_data["content_type"] == "application/octet-stream"
        assert large_data["size"] == 1024 * 1024  # 1MB
        assert len(large_data["content"]) == 1024 * 1024


class TestMockFactory:
    """测试MockFactory"""
    
    def test_create_minio_service_mock_default(self):
        """测试创建默认MinIOService Mock"""
        mock_service = MockFactory.create_minio_service_mock()
        
        assert mock_service.initialize.return_value is True
        assert mock_service.generate_presigned_put_url.return_value == "https://test-presigned-url"
        assert mock_service.upload_file.return_value is True
        assert mock_service.file_exists.return_value is True
        assert mock_service.bucket_name == "test-bucket"
    
    def test_create_minio_service_mock_custom(self):
        """测试创建自定义MinIOService Mock"""
        mock_service = MockFactory.create_minio_service_mock(
            initialize_return=False,
            presigned_url="https://custom-url",
            upload_success=False,
            file_exists=False
        )
        
        assert mock_service.initialize.return_value is False
        assert mock_service.generate_presigned_put_url.return_value is None
        assert mock_service.upload_file.return_value is False
        assert mock_service.file_exists.return_value is False
    
    def test_create_db_session_mock(self):
        """测试创建数据库会话Mock"""
        mock_session = MockFactory.create_db_session_mock()
        
        assert hasattr(mock_session, 'add')
        assert hasattr(mock_session, 'commit')
        assert hasattr(mock_session, 'rollback')
        assert hasattr(mock_session, 'close')
        assert hasattr(mock_session, 'query')
    
    def test_create_settings_mock(self):
        """测试创建设置Mock"""
        mock_settings = MockFactory.create_settings_mock()
        
        assert mock_settings.get_allowed_image_extensions.return_value == ['jpg', 'jpeg', 'png', 'gif']
        assert mock_settings.MINIO_BUCKET_NAME == "test-bucket"
        assert mock_settings.MINIO_ENDPOINT == "localhost:9001"


class TestResponseValidator:
    """测试ResponseValidator"""
    
    def test_validate_presigned_url_response_valid(self):
        """测试验证有效的预签名URL响应"""
        valid_response = {
            "file_id": "test-id",
            "object_name": "uploads/test.jpg",
            "presigned_url": "https://example.com/presigned",
            "expires_in": 3600,
            "method": "PUT"
        }
        
        assert ResponseValidator.validate_presigned_url_response(valid_response) is True
    
    def test_validate_presigned_url_response_invalid(self):
        """测试验证无效的预签名URL响应"""
        invalid_responses = [
            {},  # 空响应
            {"file_id": "test"},  # 缺少字段
            {
                "file_id": "test-id",
                "object_name": "uploads/test.jpg",
                "presigned_url": "https://example.com/presigned",
                "expires_in": "3600",  # 错误类型
                "method": "PUT"
            },
            {
                "file_id": "test-id",
                "object_name": "uploads/test.jpg",
                "presigned_url": "https://example.com/presigned",
                "expires_in": 3600,
                "method": "INVALID"  # 无效方法
            }
        ]
        
        for invalid_response in invalid_responses:
            assert ResponseValidator.validate_presigned_url_response(invalid_response) is False
    
    def test_validate_file_info_response(self):
        """测试验证文件信息响应"""
        valid_response = {
            "object_name": "uploads/test.jpg",
            "size": 1024,
            "etag": "test-etag",
            "content_type": "image/jpeg"
        }
        
        assert ResponseValidator.validate_file_info_response(valid_response) is True
        
        # 测试无效响应
        invalid_response = {
            "object_name": "uploads/test.jpg",
            "size": "1024",  # 错误类型
            "etag": "test-etag"
        }
        
        assert ResponseValidator.validate_file_info_response(invalid_response) is False
    
    def test_validate_error_response(self):
        """测试验证错误响应"""
        valid_error = {"detail": "Error message"}
        invalid_error = {"message": "Error message"}
        
        assert ResponseValidator.validate_error_response(valid_error) is True
        assert ResponseValidator.validate_error_response(invalid_error) is False


class TestPathValidator:
    """测试PathValidator"""
    
    def test_validate_uuid(self):
        """测试UUID验证"""
        valid_uuid = str(uuid.uuid4())
        invalid_uuid = "not-a-uuid"
        
        assert PathValidator.validate_uuid(valid_uuid) is True
        assert PathValidator.validate_uuid(invalid_uuid) is False
    
    def test_validate_storage_path(self):
        """测试存储路径验证"""
        valid_uuid = str(uuid.uuid4())
        
        # 测试有效路径
        valid_paths = [
            f"uploads/images/{valid_uuid}/test.jpg",
            f"processed/videos/2025/08/{valid_uuid}/video.mp4",
            f"temp/{valid_uuid}/temp.txt"
        ]
        
        for path in valid_paths:
            assert PathValidator.validate_storage_path(path) is True
        
        # 测试无效路径
        invalid_paths = [
            "invalid",  # 太短
            "uploads/test.jpg",  # 没有UUID
            "/uploads/images/test.jpg"  # 没有UUID
        ]
        
        for path in invalid_paths:
            assert PathValidator.validate_storage_path(path) is False
    
    def test_validate_storage_path_with_constraints(self):
        """测试带约束的存储路径验证"""
        valid_uuid = str(uuid.uuid4())
        path = f"uploads/images/{valid_uuid}/test.jpg"
        
        assert PathValidator.validate_storage_path(path, storage_type="uploads") is True
        assert PathValidator.validate_storage_path(path, storage_type="processed") is False
        assert PathValidator.validate_storage_path(path, category="images") is True
        assert PathValidator.validate_storage_path(path, category="videos") is False
    
    def test_validate_datetime_iso(self):
        """测试ISO datetime验证"""
        valid_datetime = "2025-08-05T12:30:45"
        invalid_datetime = "not-a-datetime"

        assert PathValidator.validate_datetime_iso(valid_datetime) is True
        assert PathValidator.validate_datetime_iso(invalid_datetime) is False


class TestAssertionHelpers:
    """测试AssertionHelpers"""
    
    def test_assert_valid_uuid(self):
        """测试UUID断言"""
        valid_uuid = str(uuid.uuid4())
        
        # 应该不抛出异常
        AssertionHelpers.assert_valid_uuid(valid_uuid)
        
        # 应该抛出异常
        with pytest.raises(AssertionError):
            AssertionHelpers.assert_valid_uuid("invalid-uuid")
    
    def test_assert_valid_storage_path(self):
        """测试存储路径断言"""
        valid_uuid = str(uuid.uuid4())
        valid_path = f"uploads/images/{valid_uuid}/test.jpg"
        
        # 应该不抛出异常
        AssertionHelpers.assert_valid_storage_path(valid_path)
        
        # 应该抛出异常
        with pytest.raises(AssertionError):
            AssertionHelpers.assert_valid_storage_path("invalid-path")
    
    def test_assert_response_structure(self):
        """测试响应结构断言"""
        valid_response = {"field1": "value1", "field2": "value2"}
        required_fields = ["field1", "field2"]
        
        # 应该不抛出异常
        AssertionHelpers.assert_response_structure(valid_response, required_fields)
        
        # 应该抛出异常
        with pytest.raises(AssertionError):
            AssertionHelpers.assert_response_structure(valid_response, ["field1", "missing_field"])
    
    def test_assert_file_content_equal(self):
        """测试文件内容断言"""
        content = b"test content"
        file_obj = BytesIO(content)
        
        # 应该不抛出异常
        AssertionHelpers.assert_file_content_equal(file_obj, content)
        
        # 验证文件指针被重置
        assert file_obj.tell() == 0
        
        # 应该抛出异常
        with pytest.raises(AssertionError):
            AssertionHelpers.assert_file_content_equal(file_obj, b"different content")
