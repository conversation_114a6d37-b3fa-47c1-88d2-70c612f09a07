"""
数据库性能和连接池集成测试
测试数据库连接池配置、性能基准、并发处理等
"""
import pytest
import time
import threading
import gc
from concurrent.futures import ThreadPoolExecutor, as_completed
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool, StaticPool
from typing import List, Dict, Any
import tempfile
import os

from app.core.database import (
    engine, SessionLocal, get_db, get_db_session, 
    check_database_connection, get_database_info
)


@pytest.fixture(scope="function")
def test_engine():
    """创建测试用的数据库引擎"""
    # 使用内存SQLite数据库进行测试
    test_engine = create_engine(
        "sqlite:///:memory:",
        echo=False,
        pool_pre_ping=True,
        pool_recycle=300,
        pool_size=5,  # 较小的连接池用于测试
        max_overflow=10,
        poolclass=QueuePool,
        connect_args={"check_same_thread": False}
    )
    
    yield test_engine
    test_engine.dispose()


@pytest.fixture(scope="function")
def test_session_factory(test_engine):
    """创建测试会话工厂"""
    TestSessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=test_engine,
        expire_on_commit=False
    )
    return TestSessionLocal


@pytest.mark.integration
class TestConnectionPoolBasics:
    """测试连接池基础功能"""
    
    def test_connection_pool_configuration(self, test_engine):
        """测试连接池配置"""
        pool = test_engine.pool
        
        # 验证连接池类型
        assert isinstance(pool, QueuePool)
        
        # 验证连接池配置
        assert pool.size() == 5  # pool_size
        assert pool._max_overflow == 10  # max_overflow
        
        # 验证初始状态
        assert pool.checkedout() == 0  # 没有连接被检出
        assert pool.checkedin() >= 0  # 检入的连接数
    
    def test_connection_acquisition_and_release(self, test_engine):
        """测试连接获取和释放"""
        pool = test_engine.pool
        initial_checkedout = pool.checkedout()
        
        # 获取连接
        conn = test_engine.connect()
        assert pool.checkedout() == initial_checkedout + 1
        
        # 释放连接
        conn.close()
        assert pool.checkedout() == initial_checkedout
    
    def test_multiple_connections(self, test_engine):
        """测试多个连接的获取"""
        pool = test_engine.pool
        connections = []
        
        try:
            # 获取多个连接
            for i in range(3):
                conn = test_engine.connect()
                connections.append(conn)
                assert pool.checkedout() == i + 1
            
            # 验证连接可用性
            for conn in connections:
                result = conn.execute(text("SELECT 1"))
                assert result.scalar() == 1
        
        finally:
            # 清理连接
            for conn in connections:
                conn.close()
            
            assert pool.checkedout() == 0
    
    def test_connection_pool_overflow(self, test_engine):
        """测试连接池溢出处理"""
        pool = test_engine.pool
        connections = []
        
        try:
            # 获取超过pool_size的连接数
            for i in range(8):  # pool_size=5, 所以会有3个溢出连接
                conn = test_engine.connect()
                connections.append(conn)
            
            # 验证溢出连接
            assert pool.checkedout() == 8
            assert pool.overflow() >= 3  # 至少3个溢出连接
        
        finally:
            # 清理连接
            for conn in connections:
                conn.close()


@pytest.mark.integration
class TestConnectionPoolPerformance:
    """测试连接池性能"""
    
    def test_connection_acquisition_speed(self, test_engine):
        """测试连接获取速度"""
        # 预热
        conn = test_engine.connect()
        conn.close()
        
        # 测试连接获取速度
        start_time = time.time()
        iterations = 100
        
        for _ in range(iterations):
            conn = test_engine.connect()
            conn.close()
        
        end_time = time.time()
        avg_time = (end_time - start_time) / iterations
        
        # 连接获取应该很快（通常小于1ms）
        assert avg_time < 0.01, f"Connection acquisition too slow: {avg_time:.4f}s"
    
    def test_connection_reuse_efficiency(self, test_engine):
        """测试连接复用效率"""
        pool = test_engine.pool
        
        # 获取并释放连接多次
        connection_ids = []
        
        for _ in range(10):
            conn = test_engine.connect()
            connection_ids.append(id(conn.connection))
            conn.close()
        
        # 由于连接池复用，应该有重复的连接ID
        unique_connections = len(set(connection_ids))
        total_connections = len(connection_ids)
        
        # 连接复用率应该大于0（即unique_connections < total_connections）
        reuse_rate = 1 - (unique_connections / total_connections)
        assert reuse_rate > 0, "No connection reuse detected"
    
    def test_session_creation_performance(self, test_session_factory):
        """测试会话创建性能"""
        start_time = time.time()
        iterations = 50
        
        for _ in range(iterations):
            session = test_session_factory()
            session.close()
        
        end_time = time.time()
        avg_time = (end_time - start_time) / iterations
        
        # 会话创建应该很快
        assert avg_time < 0.01, f"Session creation too slow: {avg_time:.4f}s"


@pytest.mark.integration
class TestConnectionPoolConcurrency:
    """测试连接池并发处理"""
    
    def test_concurrent_connection_acquisition(self, test_engine):
        """测试并发连接获取"""
        results = []
        errors = []
        
        def worker(worker_id):
            try:
                conn = test_engine.connect()
                # 模拟一些工作
                result = conn.execute(text("SELECT :id"), {"id": worker_id})
                value = result.scalar()
                results.append(value)
                time.sleep(0.01)  # 短暂持有连接
                conn.close()
            except Exception as e:
                errors.append(f"Worker {worker_id}: {str(e)}")
        
        # 创建多个线程并发获取连接
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0, f"Concurrent connection errors: {errors}"
        assert len(results) == 10
        assert sorted(results) == list(range(10))
    
    def test_connection_pool_under_load(self, test_engine):
        """测试连接池负载处理"""
        pool = test_engine.pool
        
        def database_operation(operation_id):
            try:
                conn = test_engine.connect()
                # 执行一些数据库操作
                conn.execute(text("SELECT 1"))
                time.sleep(0.001)  # 模拟处理时间
                conn.close()
                return operation_id
            except Exception as e:
                return f"Error: {str(e)}"
        
        # 使用线程池执行并发操作
        with ThreadPoolExecutor(max_workers=15) as executor:
            futures = [executor.submit(database_operation, i) for i in range(30)]
            results = [future.result() for future in as_completed(futures)]
        
        # 验证所有操作都成功完成
        successful_operations = [r for r in results if isinstance(r, int)]
        assert len(successful_operations) == 30
        
        # 验证连接池状态正常
        assert pool.checkedout() == 0  # 所有连接都应该被释放


@pytest.mark.integration
class TestDatabasePerformanceBenchmarks:
    """测试数据库性能基准"""
    
    def test_simple_query_performance(self, test_engine):
        """测试简单查询性能"""
        conn = test_engine.connect()
        
        try:
            # 预热
            conn.execute(text("SELECT 1"))
            
            # 性能测试
            start_time = time.time()
            iterations = 1000
            
            for _ in range(iterations):
                result = conn.execute(text("SELECT 1"))
                result.scalar()
            
            end_time = time.time()
            avg_time = (end_time - start_time) / iterations
            
            # 简单查询应该很快
            assert avg_time < 0.001, f"Simple query too slow: {avg_time:.6f}s"
        
        finally:
            conn.close()
    
    def test_transaction_performance(self, test_session_factory):
        """测试事务性能"""
        session = test_session_factory()
        
        try:
            # 创建测试表
            session.execute(text("""
                CREATE TABLE IF NOT EXISTS test_performance (
                    id INTEGER PRIMARY KEY,
                    value TEXT
                )
            """))
            session.commit()
            
            # 测试事务性能
            start_time = time.time()
            iterations = 100
            
            for i in range(iterations):
                session.begin()
                session.execute(text("INSERT INTO test_performance (value) VALUES (:val)"), 
                              {"val": f"test_{i}"})
                session.commit()
            
            end_time = time.time()
            avg_time = (end_time - start_time) / iterations
            
            # 事务操作性能基准
            assert avg_time < 0.01, f"Transaction too slow: {avg_time:.6f}s"
        
        finally:
            session.close()
    
    def test_batch_operation_performance(self, test_session_factory):
        """测试批量操作性能"""
        session = test_session_factory()
        
        try:
            # 创建测试表
            session.execute(text("""
                CREATE TABLE IF NOT EXISTS test_batch (
                    id INTEGER PRIMARY KEY,
                    value TEXT
                )
            """))
            session.commit()
            
            # 测试批量插入性能
            batch_size = 100
            data = [{"val": f"batch_{i}"} for i in range(batch_size)]
            
            start_time = time.time()
            
            session.execute(text("""
                INSERT INTO test_batch (value) VALUES (:val)
            """), data)
            session.commit()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 批量操作应该比单个操作快
            assert total_time < 0.1, f"Batch operation too slow: {total_time:.4f}s"
            
            # 验证数据插入成功
            count = session.execute(text("SELECT COUNT(*) FROM test_batch")).scalar()
            assert count == batch_size
        
        finally:
            session.close()


@pytest.mark.integration
class TestConnectionPoolStress:
    """测试连接池压力处理"""

    def test_connection_pool_exhaustion(self, test_engine):
        """测试连接池耗尽处理"""
        pool = test_engine.pool
        max_connections = pool.size() + pool._max_overflow  # 5 + 10 = 15

        connections = []

        try:
            # 获取所有可用连接
            for i in range(max_connections):
                conn = test_engine.connect()
                connections.append(conn)

            # 验证连接池已满
            assert pool.checkedout() == max_connections

            # 尝试获取额外连接应该会超时或失败
            start_time = time.time()
            try:
                # 设置较短的超时时间
                extra_conn = test_engine.connect()
                extra_conn.close()
                # 如果成功获取，说明连接池配置可能不同
                pytest.skip("Connection pool allows more connections than expected")
            except Exception:
                # 预期的行为：无法获取更多连接
                pass

            end_time = time.time()
            # 验证超时时间合理
            assert end_time - start_time < 5, "Connection timeout too long"

        finally:
            # 清理所有连接
            for conn in connections:
                try:
                    conn.close()
                except:
                    pass

    def test_connection_leak_detection(self, test_engine):
        """测试连接泄漏检测"""
        pool = test_engine.pool
        initial_checkedout = pool.checkedout()

        def potentially_leaky_operation():
            """模拟可能泄漏连接的操作"""
            conn = test_engine.connect()
            # 故意不关闭连接来模拟泄漏
            return conn

        # 执行可能泄漏的操作
        leaked_connections = []
        for _ in range(3):
            conn = potentially_leaky_operation()
            leaked_connections.append(conn)

        # 验证连接确实被检出
        assert pool.checkedout() == initial_checkedout + 3

        # 手动清理泄漏的连接
        for conn in leaked_connections:
            conn.close()

        # 验证连接已释放
        assert pool.checkedout() == initial_checkedout

    def test_long_running_connections(self, test_engine):
        """测试长时间运行的连接"""
        pool = test_engine.pool

        def long_running_task(duration):
            conn = test_engine.connect()
            try:
                start_time = time.time()
                while time.time() - start_time < duration:
                    # 定期执行查询保持连接活跃
                    conn.execute(text("SELECT 1"))
                    time.sleep(0.1)
                return "completed"
            finally:
                conn.close()

        # 启动长时间运行的任务
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(long_running_task, 0.5) for _ in range(3)]
            results = [future.result() for future in as_completed(futures)]

        # 验证所有任务完成
        assert all(result == "completed" for result in results)

        # 验证连接池状态正常
        assert pool.checkedout() == 0


@pytest.mark.integration
class TestDatabaseConnectionHealth:
    """测试数据库连接健康检查"""

    def test_database_connection_check(self):
        """测试数据库连接检查功能"""
        # 测试正常连接检查
        is_healthy = check_database_connection()
        assert isinstance(is_healthy, bool)

        # 如果使用真实数据库，应该返回True
        # 如果数据库不可用，应该返回False
        # 这里我们主要测试函数不会抛出异常

    def test_database_info_retrieval(self):
        """测试数据库信息获取"""
        db_info = get_database_info()
        assert isinstance(db_info, dict)

        # 检查返回的信息结构
        if "error" not in db_info:
            # 正常情况下应该包含这些字段
            expected_fields = ["database_version", "pool_status", "connection_url"]
            for field in expected_fields:
                assert field in db_info, f"Missing field: {field}"

            # 验证连接池状态信息
            pool_status = db_info["pool_status"]
            assert isinstance(pool_status, dict)

            pool_fields = ["size", "checked_in", "checked_out", "overflow"]
            for field in pool_fields:
                assert field in pool_status, f"Missing pool status field: {field}"
                assert isinstance(pool_status[field], int), f"Pool status {field} should be integer"

    def test_session_lifecycle(self):
        """测试会话生命周期"""
        # 测试get_db_session
        session = get_db_session()
        assert session is not None

        # 测试会话可用性
        result = session.execute(text("SELECT 1"))
        assert result.scalar() == 1

        # 手动关闭会话
        session.close()

        # 测试get_db生成器
        db_gen = get_db()
        db = next(db_gen)
        assert db is not None

        # 测试会话可用性
        result = db.execute(text("SELECT 1"))
        assert result.scalar() == 1

        # 清理生成器
        try:
            next(db_gen)
        except StopIteration:
            pass  # 正常结束


@pytest.mark.integration
class TestPerformanceSummary:
    """性能测试总结"""

    def test_comprehensive_performance_benchmark(self, test_engine, test_session_factory):
        """综合性能基准测试"""
        results = {}

        # 1. 连接获取性能
        start_time = time.time()
        for _ in range(50):
            conn = test_engine.connect()
            conn.close()
        results["connection_acquisition"] = (time.time() - start_time) / 50

        # 2. 会话创建性能
        start_time = time.time()
        for _ in range(50):
            session = test_session_factory()
            session.close()
        results["session_creation"] = (time.time() - start_time) / 50

        # 3. 简单查询性能
        conn = test_engine.connect()
        start_time = time.time()
        for _ in range(100):
            conn.execute(text("SELECT 1")).scalar()
        results["simple_query"] = (time.time() - start_time) / 100
        conn.close()

        # 4. 并发操作性能
        def concurrent_operation():
            conn = test_engine.connect()
            conn.execute(text("SELECT 1"))
            conn.close()

        start_time = time.time()
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(concurrent_operation) for _ in range(20)]
            for future in as_completed(futures):
                future.result()
        results["concurrent_operations"] = (time.time() - start_time) / 20

        # 验证性能基准（相对值，不是绝对值）
        assert results["connection_acquisition"] < 0.01, "Connection acquisition too slow"
        assert results["session_creation"] < 0.01, "Session creation too slow"
        assert results["simple_query"] < 0.001, "Simple query too slow"
        assert results["concurrent_operations"] < 0.01, "Concurrent operations too slow"

        # 打印性能报告
        print("\n=== Performance Benchmark Results ===")
        for operation, avg_time in results.items():
            print(f"{operation}: {avg_time:.6f}s average")
        print("=====================================")

        return results
