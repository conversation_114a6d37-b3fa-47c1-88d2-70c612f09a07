"""
任务管理API
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
import uuid
import json
import structlog
from datetime import datetime

from app.core.database import get_db
from app.schemas.task_submit import (
    TaskSubmitRequest, TaskSubmitResponse, TaskSubmitError,
    TaskType, QueueType, BatchInfo, COMMON_ERRORS
)
from app.services.queue_router import QueueRouter
from app.services.parameter_validator import ParameterValidator

logger = structlog.get_logger()
router = APIRouter(prefix="/tasks", tags=["Tasks"])


# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.task_connections: dict = {}  # task_id -> [websockets]

    async def connect(self, websocket: WebSocket, task_id: str = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if task_id:
            if task_id not in self.task_connections:
                self.task_connections[task_id] = []
            self.task_connections[task_id].append(websocket)

    def disconnect(self, websocket: WebSocket, task_id: str = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if task_id and task_id in self.task_connections:
            if websocket in self.task_connections[task_id]:
                self.task_connections[task_id].remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def send_task_update(self, task_id: str, message: dict):
        if task_id in self.task_connections:
            for connection in self.task_connections[task_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    # 连接已断开，移除
                    self.task_connections[task_id].remove(connection)


manager = ConnectionManager()


@router.post("/", response_model=TaskSubmitResponse)
async def submit_task(
    request: TaskSubmitRequest,
    db: Session = Depends(get_db)
) -> TaskSubmitResponse:
    """
    提交处理任务

    支持的任务类型：
    - 图像处理：锐化、灰度、边缘检测、伽马校正、融合、拼接、美颜、纹理转换
    - 视频处理：缩放、灰度、帧提取、边缘检测、模糊、二值化、变换、缩略图

    处理流程：
    1. 参数验证和任务类型检查
    2. 队列路由决策和批次规划
    3. 创建Task和Batch记录
    4. 提交Celery任务
    5. 返回任务信息和状态

    Args:
        request: 任务提交请求
        db: 数据库会话

    Returns:
        任务提交响应，包含任务ID、状态、批次信息等

    Raises:
        HTTPException: 当请求无效或系统错误时
    """
    try:
        logger.info(
            "task_submit_request",
            task_type=request.task_type,
            file_count=len(request.files),
            name=request.name
        )

        # 1. 参数验证
        is_valid, error_msg, validated_params = ParameterValidator.validate_task_submission(
            request.task_type,
            request.files,
            request.parameters
        )

        if not is_valid:
            raise ValueError(error_msg)

        # 2. 获取参数警告
        warnings = ParameterValidator.validate_parameter_ranges(
            request.task_type,
            validated_params
        )

        if warnings:
            logger.info(
                "task_parameter_warnings",
                task_type=request.task_type,
                warnings=warnings
            )

        # 3. 队列路由和批次策略
        batch_strategy = QueueRouter.get_batch_strategy(
            request.task_type,
            request.files
        )

        # 4. 估算执行时间
        estimated_duration = QueueRouter.estimate_duration(
            request.task_type,
            request.files
        )

        # 5. 处理复杂度评估
        complexity = ParameterValidator.estimate_processing_complexity(
            request.task_type,
            request.files,
            validated_params
        )

        # TODO: 3. 创建数据库记录 (需要在后续任务中实现)
        # - 创建Task记录
        # - 创建Batch记录
        # - 创建File记录

        # TODO: 4. 提交Celery任务 (需要在后续任务中实现)
        # - 根据批次策略分发任务
        # - 获取Celery job IDs

        # 临时响应 - 在完整实现前提供结构化响应
        mock_task_id = 12345  # 临时ID
        mock_celery_ids = ["celery-job-1", "celery-job-2"]  # 临时Celery IDs

        # 构建批次信息
        batches = []
        batch_count = batch_strategy["estimated_batches"]
        files_per_batch = len(request.files) // batch_count

        for i in range(batch_count):
            batch_files = files_per_batch
            if i == batch_count - 1:  # 最后一个批次包含剩余文件
                batch_files = len(request.files) - (i * files_per_batch)

            batches.append(BatchInfo(
                batch_id=i + 1,
                queue_type=batch_strategy["queue_type"],
                file_count=batch_files,
                estimated_duration=estimated_duration // batch_count
            ))

        response = TaskSubmitResponse(
            task_id=mock_task_id,
            status="pending",  # TaskStatus.PENDING
            name=request.name,
            task_type=request.task_type,
            created_at=datetime.now(),
            batch_count=batch_count,
            batches=batches,
            total_files=len(request.files),
            estimated_total_duration=estimated_duration,
            celery_job_ids=mock_celery_ids
        )

        logger.info(
            "task_submit_success",
            task_id=response.task_id,
            batch_count=response.batch_count,
            queue_type=batch_strategy["queue_type"],
            complexity=complexity,
            warnings_count=len(warnings)
        )

        return response

    except ValueError as e:
        logger.error("task_submit_validation_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=TaskSubmitError(
                error_code="VALIDATION_ERROR",
                error_message=str(e)
            ).dict()
        )
    except Exception as e:
        logger.error("task_submit_system_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=TaskSubmitError(
                error_code="SYSTEM_ERROR",
                error_message="系统内部错误，请稍后重试"
            ).dict()
        )


@router.get("/")
async def list_tasks(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取任务列表
    """
    # 这里应该从数据库查询用户的任务
    tasks = [
        {
            "id": "task_1",
            "status": "completed",
            "progress": 100,
            "created_at": "2025-01-05T00:00:00Z",
            "files_count": 5
        },
        {
            "id": "task_2", 
            "status": "running",
            "progress": 45,
            "created_at": "2025-01-05T01:00:00Z",
            "files_count": 10
        }
    ]
    
    return {
        "tasks": tasks,
        "total": len(tasks),
        "skip": skip,
        "limit": limit
    }


@router.get("/{task_id}")
async def get_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    获取任务详情
    """
    # 这里应该从数据库查询任务详情
    task = {
        "id": task_id,
        "status": "running",
        "progress": 75,
        "created_at": "2025-01-05T00:00:00Z",
        "updated_at": "2025-01-05T00:30:00Z",
        "config": {"quality": "high", "format": "mp4"},
        "files": ["file1.jpg", "file2.jpg"],
        "results": ["result1.mp4"]
    }

    return task


@router.post("/{task_id}/pause")
async def pause_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    暂停任务
    """
    # 这里应该暂停Celery任务
    return {"message": f"Task {task_id} paused successfully"}


@router.post("/{task_id}/resume")
async def resume_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    恢复任务
    """
    # 这里应该恢复Celery任务
    return {"message": f"Task {task_id} resumed successfully"}


@router.post("/{task_id}/cancel")
async def cancel_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    取消任务
    """
    # 这里应该取消Celery任务
    return {"message": f"Task {task_id} cancelled successfully"}


@router.get("/{task_id}/results")
async def get_task_results(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    获取任务结果
    """
    # 这里应该从数据库查询任务结果
    results = {
        "task_id": task_id,
        "status": "completed",
        "files": [
            {
                "id": "result1",
                "name": "output1.mp4",
                "size": 1024000,
                "url": "/files/download/result1"
            }
        ]
    }
    
    return results


@router.websocket("/{task_id}/ws")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """
    任务进度WebSocket连接
    """
    await manager.connect(websocket, task_id)
    try:
        while True:
            # 保持连接活跃
            data = await websocket.receive_text()
            # 可以处理客户端发送的消息
    except WebSocketDisconnect:
        manager.disconnect(websocket, task_id)


@router.get("/types")
async def get_task_types():
    """
    获取所有支持的任务类型和参数模式

    Returns:
        任务类型字典，包含每种类型的参数模式和默认值
    """
    task_types = {}

    for task_type in TaskType:
        task_types[task_type.value] = {
            "name": task_type.value,
            "display_name": _get_task_display_name(task_type),
            "category": _get_task_category(task_type),
            "parameter_schema": ParameterValidator.get_parameter_schema(task_type),
            "default_parameters": ParameterValidator.get_default_parameters(task_type),
            "description": _get_task_description(task_type)
        }

    return {
        "task_types": task_types,
        "queue_types": [qt.value for qt in QueueType],
        "supported_file_types": {
            "image": ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp"],
            "video": ["mp4", "avi", "mov", "mkv", "wmv", "flv"]
        }
    }


@router.get("/types/{task_type}/schema")
async def get_task_parameter_schema(task_type: TaskType):
    """
    获取特定任务类型的参数模式

    Args:
        task_type: 任务类型

    Returns:
        参数模式和默认值
    """
    return {
        "task_type": task_type.value,
        "parameter_schema": ParameterValidator.get_parameter_schema(task_type),
        "default_parameters": ParameterValidator.get_default_parameters(task_type),
        "complexity_info": _get_complexity_info(task_type)
    }


def _get_task_display_name(task_type: TaskType) -> str:
    """获取任务类型的显示名称"""
    display_names = {
        TaskType.IMAGE_SHARPEN: "图像锐化",
        TaskType.IMAGE_GRAYSCALE: "图像灰度转换",
        TaskType.IMAGE_EDGE_DETECTION: "图像边缘检测",
        TaskType.IMAGE_GAMMA_CORRECTION: "图像伽马校正",
        TaskType.IMAGE_FUSION: "图像融合",
        TaskType.IMAGE_STITCHING: "图像拼接",
        TaskType.BEAUTY_ENHANCEMENT: "美颜处理",
        TaskType.TEXTURE_TRANSFER: "纹理转换",

        TaskType.VIDEO_RESIZE: "视频缩放",
        TaskType.VIDEO_GRAYSCALE: "视频灰度转换",
        TaskType.VIDEO_EXTRACT_FRAME: "视频帧提取",
        TaskType.VIDEO_EDGE_DETECTION: "视频边缘检测",
        TaskType.VIDEO_BLUR: "视频模糊",
        TaskType.VIDEO_BINARY: "视频二值化",
        TaskType.VIDEO_TRANSFORM: "视频变换",
        TaskType.VIDEO_THUMBNAIL: "视频缩略图",
    }
    return display_names.get(task_type, task_type.value)


def _get_task_category(task_type: TaskType) -> str:
    """获取任务类型的分类"""
    if task_type.value.startswith("image_"):
        return "image"
    elif task_type.value.startswith("video_"):
        return "video"
    else:
        return "other"


def _get_task_description(task_type: TaskType) -> str:
    """获取任务类型的描述"""
    descriptions = {
        TaskType.IMAGE_SHARPEN: "增强图像的清晰度和细节",
        TaskType.IMAGE_GRAYSCALE: "将彩色图像转换为灰度图像",
        TaskType.IMAGE_EDGE_DETECTION: "检测图像中的边缘和轮廓",
        TaskType.IMAGE_GAMMA_CORRECTION: "调整图像的亮度和对比度",
        TaskType.IMAGE_FUSION: "将两张图像融合为一张",
        TaskType.IMAGE_STITCHING: "将多张图像拼接成全景图",
        TaskType.BEAUTY_ENHANCEMENT: "美颜处理，包括磨皮和瘦脸",
        TaskType.TEXTURE_TRANSFER: "将一张图像的纹理转移到另一张图像",

        TaskType.VIDEO_RESIZE: "调整视频的分辨率和尺寸",
        TaskType.VIDEO_GRAYSCALE: "将彩色视频转换为灰度视频",
        TaskType.VIDEO_EXTRACT_FRAME: "从视频中提取指定帧",
        TaskType.VIDEO_EDGE_DETECTION: "对视频进行边缘检测处理",
        TaskType.VIDEO_BLUR: "对视频应用模糊效果",
        TaskType.VIDEO_BINARY: "将视频转换为二值化效果",
        TaskType.VIDEO_TRANSFORM: "对视频进行旋转、翻转等变换",
        TaskType.VIDEO_THUMBNAIL: "生成视频缩略图",
    }
    return descriptions.get(task_type, "")


def _get_complexity_info(task_type: TaskType) -> Dict[str, Any]:
    """获取任务复杂度信息"""
    complexity_info = {
        "base_complexity": "medium",
        "factors": [],
        "estimated_time_per_mb": 0.5
    }

    # 低复杂度任务
    low_complexity_tasks = {
        TaskType.IMAGE_GRAYSCALE, TaskType.VIDEO_THUMBNAIL,
        TaskType.VIDEO_EXTRACT_FRAME
    }

    # 高复杂度任务
    high_complexity_tasks = {
        TaskType.IMAGE_STITCHING, TaskType.TEXTURE_TRANSFER,
        TaskType.VIDEO_EDGE_DETECTION
    }

    if task_type in low_complexity_tasks:
        complexity_info["base_complexity"] = "low"
        complexity_info["estimated_time_per_mb"] = 0.2
        complexity_info["factors"].append("快速处理")
    elif task_type in high_complexity_tasks:
        complexity_info["base_complexity"] = "high"
        complexity_info["estimated_time_per_mb"] = 1.5
        complexity_info["factors"].append("复杂算法")

    return complexity_info
